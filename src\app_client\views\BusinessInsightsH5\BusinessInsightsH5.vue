<template>
    <div class="business-insights-h5 hide-scrollbar" v-ai-tip="'center'">
        <van-tabs v-model="activeName" @click-tab="onClickTab" color="#436BFF">
            <van-tab title="客户之声" name="CUSTOMER_VOICE">
                <customerVoice v-if="activeName === 'CUSTOMER_VOICE'" ref="refCustomerVoice" />
            </van-tab>
            <van-tab title="需求洞察" name="requirementInsight">
                <requirementInsight v-if="activeName === 'requirementInsight'" ref="refRequirementInsight" />
            </van-tab>
            <van-tab title="竞品雷达" name="COMPETITOR_RADAR">
                <competitiveRadar v-if="activeName === 'COMPETITOR_RADAR'" ref="refCompetitiveRadar" />
            </van-tab>
        </van-tabs>

    </div>
    <div v-if="isShowFloating">
        <van-floating-bubble axis="xy" icon="chat" magnetic="x" @click="showPopover = !showPopover">
            <!-- 悬浮内容 -->
            <el-tooltip placement="left" effect="light" :visible="showPopover">
                <template #default>
                    <img class="show-popover-img" :src="getOssUrl('buttonb.png', 3)" alt="">
                </template>
                <template #content>
                    <div class="popover-content">
                        <p v-for="item in actions[activeName]" :key="item.key" @click="onClickToTable(item.key)">{{
                            item.text }}</p>
                    </div>
                </template>
            </el-tooltip>
        </van-floating-bubble>
    </div>
</template>

<script setup>
import competitiveRadar from './competitiveRadar/competitiveRadar.vue'
import customerVoice from './customerVoice/customerVoice.vue'
import requirementInsight from './requirementInsight/requirementInsight.vue'
const activeName = ref('CUSTOMER_VOICE');
import { getOssUrl } from '@/js/utils.js';
const showPopover = ref(false);

const isShowFloating = ref(true)
// 为子组件添加 ref 引用
const refCustomerVoice = ref(null);
const refRequirementInsight = ref(null);
const refCompetitiveRadar = ref(null);

const onClickTab = (tab) => {
    activeName.value = tab.name;
}

const actions = {
    'CUSTOMER_VOICE':
        [
            { text: '总览', key: 'refOverview' },
            { text: '客户反馈', key: 'refFeedback' },
            { text: '常见问题', key: 'refFAQ' }
        ],
    'COMPETITOR_RADAR':
        [{ text: '总览', key: 'refOverview' },
        { text: '竞品分析', key: 'refAnalysis' },
        { text: '竞品评价', key: 'refEvaluation' }]
}


// 点击跳转到对应标题位置
const onClickToTable = (key) => {
    showPopover.value = false; // 关闭弹窗
    // 根据当前激活的标签页调用对应的子组件方法
    if (activeName.value === 'CUSTOMER_VOICE' && refCustomerVoice.value) {
        refCustomerVoice.value.onClickToTable(key);
    } else if (activeName.value === 'COMPETITOR_RADAR' && refCompetitiveRadar.value) {
        refCompetitiveRadar.value.onClickToTable(key);
    }
};

watch(() => activeName.value, (newVal) => {
    isShowFloating.value = ['CUSTOMER_VOICE', 'COMPETITOR_RADAR'].includes(newVal)
    g.clientBiStore.setSelectTabType(newVal)
}, { immediate: true })

onBeforeMount(() => {
    try {
        document.title = '业务洞察'
        window.flutter_inappwebview.callHandler("clearWebViewHistory");
    } catch (e) {
        console.log("非app端执行，无法与原生进行通讯");
    }
})

</script>
<style lang="scss">
#app {
    background: #F5F5F5;
}

.business-insights-h5 {
    .el-empty {
        --el-empty-image-width: 50vw !important;
    }

    height: calc(100vh - 10px);
    overflow-y: auto;
    box-sizing: border-box;

    .van-tabs {
        //   border-bottom: 1px solid #E9E9E9;
    }

    .van-tab__text {
        font-size: 14px;
        color: #595959;
    }

}

.show-popover-img {
    width: 24px;
    height: 24px;
}

.popover-content {
    p {
        padding: 12px;
        margin: 0;
        font-weight: 400;
        font-size: 14px;
        color: #262626;
        line-height: 22px;
        text-align: left;
        min-width: 100px;
    }
}
</style>