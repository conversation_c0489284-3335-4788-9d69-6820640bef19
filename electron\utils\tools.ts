import { app } from "electron";
import { exec } from 'child_process';
import { Base64 } from 'js-base64'
import fs from 'fs'
import path from 'node:path'
import crypto from 'node:crypto'
import os from 'os';
import { screen } from 'electron';
import { getStore } from "./store";

export function getCurrentDateFormatted() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
}

export function getClientId() {
    return crypto.randomUUID()
}

export function handleProtocolUrl(_argv: string[], createSubWindows, callback = () => { }) {
    // 获取协议URL参数
    const protocolUrl = _argv.find(arg => arg.startsWith('yxtnovaguide://'))

    if (protocolUrl) {
        try {
            // 从协议URL中提取参数
            const [_, params] = protocolUrl.split('yxtnovaguide://')
            const paramPairs = params.split('&')
            const paramMap = {}

            paramPairs.forEach(pair => {
                const [key, value] = pair.split('=')
                paramMap[key] = value
            })

            const { action, data }: any = paramMap

            if (action && data) {
                const decodedData = JSON.parse(Base64.decode(data))

                // 创建窗口参数
                const windowArg = {
                    urlParam: {
                        id: action,
                        url: `/electron/${action}`
                    },
                    newPageData: decodedData
                }

                // 打开新窗口
                createSubWindows(JSON.stringify(windowArg))
            } else {
                callback()
            }
        } catch (error) {
            callback()
        }
    } else {
        callback()
    }
}

export function getSystemInfo() {
    return new Promise((resolve) => {
        const info = {
            platform: os.platform(),
            release: os.release(),
            arch: os.arch(),
            hostname: os.hostname(),
            brand: '',
            model: ''
        };

        if (process.platform === 'win32') {
            // Windows
            exec('wmic csproduct get vendor,name', (error, stdout) => {
                if (!error) {
                    const lines = stdout.split('\n').filter(line => line.trim());
                    if (lines.length > 1) {
                        const [vendor, name] = lines[1].trim().split(/\s{2,}/);
                        info.brand = vendor;
                        info.model = name;
                    }
                }
                resolve(info);
            });
        } else if (process.platform === 'darwin') {
            // macOS
            exec('system_profiler SPHardwareDataType', (error, stdout) => {
                if (!error) {
                    const modelNameMatch = stdout.match(/Model Name: (.*)/);
                    info.brand = 'Apple';
                    info.model = modelNameMatch ? modelNameMatch[1].trim() : '';
                }
                resolve(info);
            });
        } else {
            // Linux
            exec('cat /sys/class/dmi/id/sys_vendor; cat /sys/class/dmi/id/product_name', (error, stdout) => {
                if (!error) {
                    const [vendor, name] = stdout.split('\n');
                    info.brand = vendor.trim();
                    info.model = name.trim();
                }
                resolve(info);
            });
        }
    });
}

// 获取本地网站资源目录
export const getLocalWebPath = () => {
    const userDataPath = app.getPath('userData');
    const storePrefix = getStorePrefix();
    const webResourcePath = path.join(userDataPath, storePrefix + 'web-resources');

    // Ensure directories exist
    if (!fs.existsSync(userDataPath)) {
        fs.mkdirSync(userDataPath, { recursive: true });
    }
    if (!fs.existsSync(webResourcePath)) {
        fs.mkdirSync(webResourcePath, { recursive: true });
    }

    return webResourcePath;
}

// 获取升级包地址
export const getUpgradeBaseUrl = function () {
    const isProdApi = import.meta.env.VITE_APP_APIENV.indexOf('prod') > -1;
    let prex = ''
    if (isProdApi) {
        const isBlue = getStore('isBlue');
        prex = isBlue ? 'blue-' : ''
    } else {
        prex = 'dev-'
    }
    const url = `https://meetcdn.yxt.com/${prex}download-novaguide`;
    console.log('getUpgradeBaseUrl', url);
    return url;
}

// export const getLocalVersionInfo = function () {
export const getLocalResourceInfo = function () {
    // Ensure web-resources directory exists
    const webResourcePath = getLocalWebPath();
    const webResourceVersionPath = path.join(webResourcePath, 'version.json');
    const asarPath = app.getAppPath();
    const asarDistPath = path.join(asarPath, 'dist');
    const asarVersionPath = path.join(asarDistPath, 'version.json');
    const asarIndexPath = path.join(asarDistPath, 'index.html');

    let webResourceInfo = null;
    let asarInfo = null;

    // 读取 web-resources 目录的版本信息
    try {
        if (fs.existsSync(webResourceVersionPath)) {
            webResourceInfo = JSON.parse(fs.readFileSync(webResourceVersionPath, 'utf-8'));
            webResourceInfo['indexPath'] = path.join(webResourcePath, webResourceInfo.version, 'dist', 'index.html');
        } else {
            // console.log("reading web resource version: webResourceVersionPath not found", webResourceVersionPath);
        }
    } catch (error) {
        console.error('Error reading web resource version:', error);
    }

    // 读取 asar 包内的版本信息
    try {
        if (fs.existsSync(asarVersionPath)) {
            asarInfo = JSON.parse(fs.readFileSync(asarVersionPath, 'utf-8'));
            asarInfo['indexPath'] = asarIndexPath;
        } else {
            console.error("Error reading asar version: asarVersionPath not found", asarVersionPath);
        }
    } catch (error) {
        console.error('Error reading asar version:', error);
    }

    if (asarInfo) {
        if (webResourceInfo) {
            if (asarInfo.version > webResourceInfo.version) {
                // console.log("asarInfo.version > webResourceInfo.version, return asarInfo", asarInfo);
                return asarInfo;
            } else if (asarInfo.version === webResourceInfo.version) {
                // console.log("asarInfo.version === webResourceInfo.version, return asarInfo", asarInfo, webResourceInfo);
                return asarInfo.buildTime > webResourceInfo.buildTime ? asarInfo : webResourceInfo;
            } else {
                // console.log("webResourceInfo.version > asarInfo.version, return webResourceInfo", webResourceInfo);
                return webResourceInfo;
            }
        } else {
            console.log("no webResourceInfo, return asarInfo", asarInfo);
            return asarInfo
        }
    } else {
        const defaultInfo = {
            version: app.getVersion(),
            buildTime: '',
            indexPath: asarIndexPath
        };
        console.log("no asarInfo, return defaultInfo", defaultInfo);
        return defaultInfo;
    }
}

export const getStorePrefix = () => {
    const apiEnv = import.meta.env.VITE_APP_APIENV;
    let storePrefix = ''
    if (apiEnv.indexOf('prod') == -1) {
        storePrefix = apiEnv + '-'
    }
    return storePrefix;
}

export const getMainScreenInfo = async () => {
    const primaryDisplay = screen.getPrimaryDisplay();
    return primaryDisplay.workArea;
}

export const addWindowsEvents = (win) => {
    const events = ['restore', 'close', 'minimize', 'maximize', 'unmaximize', 'ready-to-show', 'hide', 'show', 'focus', 'blur', 'close']
    events.forEach(event => {
        win.on(event, () => {
            if (!win.isDestroyed()) {
                win.webContents.send('window-event', event);
            }
        });
    });
}


// 添加以下函数
export function formatDate(fdate, format = 'YYYY-MM-dd') {
    if (!fdate) {
        return ''
    }

    // 处理日期字符串的兼容性问题
    if (typeof fdate === 'string') {
        // 替换连字符为斜杠以提高跨浏览器兼容性
        fdate = fdate.replace(/-/g, '/');
    }

    // 创建日期对象并验证
    const dateObj = new Date(fdate);
    if (isNaN(dateObj.getTime())) {
        console.error('Invalid date:', fdate);
        return '';
    }

    var date = {
        "Y+": dateObj.getFullYear(),
        "M+": dateObj.getMonth() + 1,
        "d+": dateObj.getDate(),
        "h+": dateObj.getHours(),
        "m+": dateObj.getMinutes(),
        "s+": dateObj.getSeconds(),
        "q+": Math.floor((dateObj.getMonth() + 3) / 3),
        "S+": dateObj.getMilliseconds(),
    }

    if (/(y+)/i.test(format)) {
        format = format.replace(RegExp.$1, (dateObj.getFullYear() + '').substr(4 - RegExp.$1.length))
    }

    for (var k in date) {
        if (new RegExp("(" + k + ")").test(format)) {
            format = format.replace(RegExp.$1, RegExp.$1.length === 1
                ? date[k] : ("00" + date[k]).substr(("" + date[k]).length))
        }
    }
    return format
}

export function now(dt_dormat = 'yyyy-MM-dd hh:mm:ss') {
    return formatDate(new Date(), dt_dormat);
}