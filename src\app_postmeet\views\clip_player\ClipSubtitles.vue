<template>
  <div ref="subtitlesContainer" v-ai-tip="{ type: 'center', text: '以上字幕由AI生成，仅供参考' }" :class="wrap_class">
    <div v-for="(item, index) in sentences" :key="index" class="subtitle-item" :class="{ active: isActive(item) }" :ref="(el) => {
      if (el) subtitleRefs[index] = el;
    }
      " @click="handleSubtitleClick(item)">
      <div class="message-content">
        <div class="user-info">
          <div class="time">{{ item.startTime }}</div>
          <span class="username" v-html="item.info.displayName"></span>
        </div>
        <div class="content" v-html="item.content"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { toReplace } from "@/app_postmeet/tools/tools";

const props = defineProps({
  sentences: {
    type: Array,
    default: () => [],
  },
});

const subtitlesContainer = ref(null);
const subtitleRefs = ref([]);
const currentTime = ref(0);
const isShowVideo = ref(localStorage.getItem("isPlayVideo") == "true");
const searchKeyword = ref("");
const searchIndex = ref(0);
const searchCount = ref(0);
const originalSentences = ref([]);
const auidoHasError = ref(false)
const wrap_class = ref('clip-subtitles cs_video_show')

const getTimeInSeconds = (timeString) => {
  const [hours, minutes, seconds] = timeString.split(":").map(Number);
  return 3600 * hours + 60 * minutes + seconds;
};

const isActive = (item) => {
  const currentTimeInSeconds = currentTime.value;
  const itemStartTime = getTimeInSeconds(item.startTime);
  const nextItemIndex = props.sentences.indexOf(item) + 1;
  const nextItemTime =
    nextItemIndex < props.sentences.length
      ? getTimeInSeconds(props.sentences[nextItemIndex].startTime)
      : Infinity;

  return currentTimeInSeconds >= itemStartTime && currentTimeInSeconds < nextItemTime;
};

const handleSubtitleClick = (item) => {
  const timeInSeconds = getTimeInSeconds(item.startTime);
  g.emitter.emit("updateVideoTime", timeInSeconds);
};

const highlightSearchText = (text, keyword) => {
  if (!keyword) return text;
  const str = `<span style='color: #262626;background: #FFDEA4'>${keyword}</span>`;
  return toReplace(text, keyword, str);
};

const handleSearch = (keyword) => {
  if (!originalSentences.value.length) return;

  searchKeyword.value = keyword;
  let count = 0;

  props.sentences.forEach((item, index) => {
    const originalItem = originalSentences.value[index];
    if (!originalItem) return;

    item.content = originalItem.content;
    item.info.displayName = originalItem.info.displayName;

    if (keyword) {
      if (item.content.includes(keyword) || item.info.displayName.includes(keyword)) {
        item.content = highlightSearchText(item.content, keyword);
        item.info.displayName = highlightSearchText(item.info.displayName, keyword);
        count++;
      }
    }
  });

  searchCount.value = count;
  searchIndex.value = count > 0 ? 1 : 0;
  g.emitter.emit("set_search_input", { count, index: searchIndex.value });

  if (count > 0) {
    scrollToHighlight(1);
  }
};

const scrollToHighlight = (index) => {
  let currentCount = 0;
  for (let i = 0; i < props.sentences.length; i++) {
    const item = props.sentences[i];
    if (
      item.content.includes("background: #FFDEA4") ||
      item.info.displayName.includes("background: #FFDEA4")
    ) {
      currentCount++;
      if (currentCount === index) {
        subtitleRefs.value[i]?.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
        break;
      }
    }
  }
};

const setAudioError = () => {
  auidoHasError.value = true;
}

const _update_class = () => {
  if (isShowVideo.value) {
    if (auidoHasError.value) {
      wrap_class.value = 'clip-subtitles cs_video_show_error';
    } else {
      wrap_class.value = 'clip-subtitles cs_video_show';
    }
  } else {
    wrap_class.value = 'clip-subtitles cs_video_hide';
  }
}


watch(() => [isShowVideo.value, auidoHasError.value], () => {
  _update_class()
}, { immediate: true })

watch(
  () => props.sentences,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      originalSentences.value = JSON.parse(JSON.stringify(newVal));
    }
  },
  { immediate: true }
);

onMounted(() => {
  g.emitter.on("timeUpdate", (time) => {
    currentTime.value = time;
    const activeIndex = props.sentences.findIndex((item) => isActive(item));
    if (activeIndex !== -1 && subtitleRefs.value[activeIndex]) {
      subtitleRefs.value[activeIndex].scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  });

  g.emitter.on("playing_video", (isPlayVideo) => {
    isShowVideo.value = isPlayVideo;
  });

  g.emitter.on("right_header_search", ([action, data]) => {
    switch (action) {
      case "keyword":
        handleSearch(data);
        break;
      case "clear":
        handleSearch("");
        break;
      case "up":
        if (searchCount.value > 0) {
          searchIndex.value = data;
          scrollToHighlight(searchIndex.value);
        }
        break;
      case "down":
        if (searchCount.value > 0) {
          searchIndex.value = data;
          scrollToHighlight(searchIndex.value);
        }
        break;
    }
  });
});

defineExpose({ isActive, setAudioError });
</script>

<style lang="scss" scoped>
.cs_video_hide {
  height: calc(100vh - 320px);
}

.cs_video_show {
  height: calc(100vh - 354px);
}

.cs_video_show_error {
  height: calc(100vh - 160px);
}

.clip-subtitles {
  padding: 20px;
  margin-top: 20px;
  overflow-y: auto;
  border-top: 1px solid #eee;

  .subtitle-item {
    display: flex;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    cursor: pointer;

    &.active {
      .message-content {
        .content {
          background: #e6f7ff;
          border: 1px solid #91d5ff;
        }

        .user-info {
          .time {
            color: #436bff;
            font-weight: bold;
          }
        }
      }
    }

    .message-content {
      width: 100%;

      .user-info {
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        gap: 8px;

        .time {
          color: #999;
          font-size: 14px;
        }

        .username {
          color: #666;
          font-size: 14px;
        }
      }

      .content {
        background: #eff2fc;
        border-radius: 0px 8px 8px 8px;
        padding: 12px;
        line-height: 1.5;
        display: inline-block;
        max-width: 96%;
        font-size: 14px;
        color: #333;
      }
    }
  }
}
</style>
