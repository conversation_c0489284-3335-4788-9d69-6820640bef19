<template>
    <div class="av_item_value datetime-picker flex-col">
        <div class="dt_item flex-row">
            <el-date-picker v-model="localParam.startDate" type="date" placeholder="开始日期" value-format="YYYY-MM-DD"
                :disabledDate="disabledStartDate" @change="onStartDateChange" />
            <el-time-select v-model="localParam.startTime" style="width: 240px" :start="timePickerStart" step="00:15"
                end="23:49" placeholder="开始时间" format="HH:mm" @change="onStartTimeChange" />
        </div>
        <div class="dt_item flex-row">
            <el-date-picker v-model="localParam.endDate" type="date" placeholder="结束日期" value-format="YYYY-MM-DD"
                :disabledDate="disabledEndDate" @change="onEndDateChange" />
            <el-time-select v-model="localParam.endTime" style="width: 240px" :start="endTimePickerStart" step="00:15"
                end="23:59" placeholder="结束时间" format="HH:mm" @change="onEndTimeChange" />
        </div>
    </div>
</template>

<script setup>
// import { nowMNAfter } from '@/js/utils'
import { ElMessage, dayjs } from 'element-plus'

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
})
const localParam = ref(props.modelValue)

const emit = defineEmits(['update:modelValue'])

const timePickerStart = computed(() => {
    // if (localParam.value.startDate) {
    //     const today = dayjs().format('YYYY-MM-DD')
    //     if (localParam.value.startDate === today) {
    //         return dayjs().add(30, 'minute').format('HH:mm')
    //     }
    // }
    return '00:00'
})

const endTimePickerStart = computed(() => {
    // if (localParam.value.endDate && localParam.value.startDate) {
    //     if (localParam.value.endDate === localParam.value.startDate) {
    //         const startDateTime = dayjs(`${localParam.value.startDate} ${localParam.value.startTime || '00:00'}`)
    //         return startDateTime.add(60, 'minute').format('HH:mm')
    //     }
    // }
    return '00:00'
})

const onStartDateChange = (val) => {
    localParam.value.startDate = val
    const today = dayjs().format('YYYY-MM-DD')

    if (val === today) {
        // 设置为下一个整半点时间
        localParam.value.startTime = getNextHalfHour()

        // 自动设置结束时间为开始时间后1小时
        const startDateTime = dayjs(`${val} ${localParam.value.startTime}`)
        const endDateTime = startDateTime.add(1, 'hour')
        localParam.value.endTime = endDateTime.format('HH:mm')
    }

    if (localParam.value.endDate && dayjs(localParam.value.endDate).isBefore(dayjs(val))) {
        localParam.value.endDate = val
    }
    emit('update:modelValue', localParam.value)
}

const onStartTimeChange = (val) => {
    localParam.value.startTime = val

    // 检查如果是当天，且选择的时间小于当前时间
    const today = dayjs().format('YYYY-MM-DD')
    if (localParam.value.startDate === today) {
        const now = dayjs()
        const selectedDateTime = dayjs(`${localParam.value.startDate} ${val}`)

        if (selectedDateTime.isBefore(now)) {
            const nextTime = getNextHalfHour()
            ElMessage.warning('开始时间不能小于当前时间，已自动调整为最近的整半点时间')
            localParam.value.startTime = nextTime
        }
    }

    // 自动设置结束时间为开始时间后1小时
    if (localParam.value.startTime) {
        const startDateTime = dayjs(`${localParam.value.startDate || dayjs().format('YYYY-MM-DD')} ${localParam.value.startTime}`)
        const endDateTime = startDateTime.add(1, 'hour')
        localParam.value.endTime = endDateTime.format('HH:mm')
    }

    emit('update:modelValue', localParam.value)
}

// 添加一个方法来获取下一个整半点时间
const getNextHalfHour = (date) => {
    const now = date ? dayjs(date) : dayjs()
    const currentMinutes = now.minute()

    let nextHalfHour
    if (currentMinutes < 30) {
        nextHalfHour = now.minute(30).second(0).millisecond(0)
    } else {
        nextHalfHour = now.add(1, 'hour').minute(0).second(0).millisecond(0)
    }

    return nextHalfHour.format('HH:mm')
}

const onEndDateChange = (val) => {
    localParam.value.endDate = val
    if (val === localParam.value.startDate && localParam.value.startTime) {
        if (!localParam.value.endTime || dayjs(`${val} ${localParam.value.endTime}`).isSameOrBefore(dayjs(`${val} ${localParam.value.startTime}`))) {
            const startDateTime = dayjs(`${val} ${localParam.value.startTime}`)
            const endDateTime = startDateTime.add(1, 'hour')
            localParam.value.endTime = endDateTime.format('HH:mm')
        }
    }
    emit('update:modelValue', localParam.value)
}

const onEndTimeChange = (val) => {
    localParam.value.endTime = val
    emit('update:modelValue', localParam.value)
}

const disabledStartDate = (time) => {
    return dayjs(time).isBefore(dayjs().subtract(1, 'day'), 'day')
}

const disabledEndDate = (time) => {
    const startDate = localParam.value.startDate ? dayjs(localParam.value.startDate) : null
    if (!startDate) return false

    const timeDate = dayjs(time)
    const nextDay = startDate.add(1, 'day')

    return timeDate.isBefore(startDate, 'day') || timeDate.isAfter(nextDay, 'day')
}

watch(() => props.modelValue, (newVal) => {
    localParam.value = newVal
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.av_item_value {
    .dt_item {
        margin: 5px 0;

        :deep(.el-date-editor) {
            margin: 0 5px !important;
            width: 280px !important;
        }
    }
}
</style>
