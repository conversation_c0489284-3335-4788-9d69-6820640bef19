<template>
  <div class="la_box2 la_main flex-column">
    <div class="la_header flex-col" v-if="avgScore != '-'">
      <div class="la_head1 flex-row">
        <div class="la_title flex-row">
          <div class="la_title_text">能力评估</div>
          <div class="la_score_unit">满分100分，{{ targetSettings }}分达标</div>
        </div>
        <div class="la_detail">
          <slot name="detail" />
        </div>
      </div>
      <div class="la_label1 flex-row">
        <div :class="`la_score flex-row   ${getStatusClass(avgScore)}`">
          <img class="la_score_icon2" :src="getOssUrl(getIconName(avgScore))" alt="" v-if="avgScore != '-'" />
          <div class="la_score_num_box flex-row">
            <div :class="`la_score_num  ${avg_style}`">
              {{ avgScore }}
            </div>
            <div class="la_score_unit">分</div>
          </div>
        </div>
        <el-button type="primary" size="small" class="la_btn" @click="onUpgrade"
          v-if="isShowGoUpgrade && avgScore < targetSettings">
          去提升
        </el-button>
        <div class="la_tip" v-show="isShowGoUpgrade && avgScore < targetSettings">
          <div v-if="process == 0"></div>
          <div v-else-if="process == 100">恭喜，已完成提升计划</div>
          <div v-else>提升进度 {{ process }}%，继续加油</div>
        </div>
      </div>
    </div>
    <div class="la_box2 la_main flex-column" v-else>
      <div v-if="isLoading" v-loading="isLoading">
        <el-empty description="内容生成中，请稍后..." />
      </div>
      <el-empty description="" v-else>
        <div class="empty_desc flex-row">
          <div>内容生成失败，可</div>
          <div class="empty_desc_tip" @click="onRetry">重试</div>
        </div>
      </el-empty>
    </div>
  </div>
</template>

<script>
import { getConferenceKnowledgePackage, getCheckFactor } from "@/js/api";
import { generateSaleReportForce, getSaleReports } from "@/app_postmeet/tools/api";
import { getOssUrl } from "@/js/utils.js";
export default {
  name: "AbilityAssessment",
  props: {
    status: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      process: 0,
      targetSettings: 0,
      avgScore: '-',
      avg_style: "",
      isComplete: true,
      isHost: false,
      isLoading: false,
      timer: null,
      isShowGoUpgrade: false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    onRetry() {
      console.log('onRetry')
      this.isLoading = true;
      let ar =
        g.postmeetStore.data.saleReport.salesCounsellingReports.find(
          (x) => x.systemId == 205
        )
      console.log('ar', ar)
      const param = {
        "dimensionId": ar.id,
        "forceRenewDimension": true,
      }
      generateSaleReportForce(g.postmeetStore.data.confId, param).then((res) => {
        g.emitter.emit("monitor_sale_report", ar.id);
        this._monitorReportStatus();
        // this.isLoading = false;
      }).catch((err) => {
        console.log('generateSaleReportForce error', err)
        this.isLoading = false;
      })
    },
    _monitorReportStatus() {
      const that = this;
      function fn() {
        !!that.timer && clearInterval(that.timer);
      }
      fn();
      this.isLoading = true;
      this.timer = setInterval(() => {
        getSaleReports(g.postmeetStore.data.confId).then((resp) => {
          if (resp.code == 0) {
            const { status, complete } = resp.data;
            if (status == "SUCCESS" || status == "FAIL") {
              g.postmeetStore.setValue("isGenReporting", false);
              g.emitter.emit("is_re_gen_sale_report", false);
              this.isLoading = false;
              if (status == "SUCCESS") {
                location.reload();
              }
              fn();
            } else {
              g.emitter.emit("update_report_complete_process", complete);
              this._monitorReportStatus();
            }
          }
        });
      }, 10000);
    },
    getOssUrl,
    getIconName(score) {
      const targetSettings = g.postmeetStore.data.targetSettings;
      console.log("getIconName", score, targetSettings)
      if (score >= targetSettings) {
        return "coche_success.png";
        // } else if (score >= 60) {
        //   return "coche_normal.png";
      } else {
        return "coche_fail.png";
      }
    },
    init() {
      this.isHost = !g.postmeetStore.isReadonly();
      let ar =
        g.postmeetStore.data.saleReport.salesCounsellingReports.find(
          (x) => x.systemId == 205
        );
      let arTask = ar.report?.tasks || [];
      arTask = arTask.filter(x => x?.content?.score);
      arTask.forEach((item) => {
        item.content["score2"] = parseInt(item.content.score.replace("/100", ""));
      });
      if (arTask.length === 0) {
        this.avgScore = '-';
      } else {
        this.avgScore = Math.round(
          arTask.reduce((sum, item) => sum + item.content.score2, 0) / arTask.length
        );
      }
      if (this.isHost) {
        this.getCheckFactor();
      }
      this.targetSettings = ar.targetSettings || 0;
      this.avg_style = this.getStatusClass(this.avgScore);
    },
    getStatusClass(score) {
      return score > this.targetSettings ? "s2" : score > 60 ? "s1" : "s0";
    },
    getKnowledgePackage() {
      this.process = g.postmeetStore.data.kngStudyProcess;
      if (this.process != 0) {
        return;
      }
      getConferenceKnowledgePackage(g.postmeetStore.data.confId).then((res) => {
        if (res.code == 0) {
          const { completedCount, totalCount } = res.data;
          this.process = Math.round((100 * completedCount) / totalCount);
          g.postmeetStore.setValue("kngStudyProcess", this.process);
        }
      });
    },
    getCheckFactor() {
      const salesPromoteCourseId = g.postmeetStore.data.asrRaw.salesPromoteCourseId || "";
      if (salesPromoteCourseId) {
        this.isShowGoUpgrade = true;
        this.getKnowledgePackage();
      } else {
        getCheckFactor(g.postmeetStore.data.confId).then((res) => {
          this.isShowGoUpgrade = res.data;
        });
      }
    },
    onUpgrade() {
      const url = `${g.config.publicPath}/#/player/${g.postmeetStore.data.confId}`;
      window.open(url, "_blank");
    },
  },
};
</script>
<style lang="scss">
.la_box2 {
  .empty_desc {
    .empty_desc_tip {
      color: #436bff;
      margin-left: 4px;
      cursor: pointer;
    }
  }

  .la_head1 {
    width: 100%;
    justify-content: space-between;

    .la_title {
      .la_title_text {
        height: 22px;
        font-weight: 500;
        font-size: 14px;
        color: #262626;
        line-height: 22px;
      }

      .la_score_unit {
        height: 20px;
        font-size: 12px;
        color: #8c8c8c;
        line-height: 20px;
        margin-left: 4px;
      }
    }

    .la_detail {
      height: 22px;
      font-size: 14px;
      color: #436bff;
      line-height: 22px;
      cursor: pointer;
    }
  }
}

.la_label1 {
  margin-top: 12px;
  display: flex;
  align-items: center;

  .la_score {
    margin-right: 16px;

    .la_score_icon2 {
      width: 32px;
      height: 32px;
    }

    .la_score_num_box {
      margin-left: 4px;
      margin-top: 2px;

      .la_score_num {
        font-weight: 500;
        font-size: 20px;
        line-height: 30px;
      }

      .la_score_unit {
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        margin-top: 6px;
      }
    }
  }

  .la_btn {
    width: 62px;
    height: 28px;
    background: linear-gradient(270deg, #fe823f 0%, #fcbb22 100%);
    border-radius: 4px;
  }

  .la_tip {
    margin-left: 12px;
    height: 22px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #fa541c;
    line-height: 22px;
    text-align: justify;
    font-style: normal;
  }
}
</style>
