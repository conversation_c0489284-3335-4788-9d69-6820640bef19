<template>
  <div class="right_header_wrap">
    <div class="rh_title">
      <span>沟通回顾</span>
      <SelectRecord />
    </div>

    <div class="rh_right">
      <div v-show="isShowClipBtn && isShow && !hideIcons.includes('clip')">
        <btnClipper />
      </div>
      <!-- 有字幕才展示操作按钮 -->
      <template v-if="hasTextPolish">
        <div v-show="isShow && !hideIcons.includes('filter')">
          <btnFilter />
        </div>
        <btnSearch @callback="cbBtn" v-show="!hideIcons.includes('search')" />
      </template>
      <!-- <div v-show="isShow && !hideIcons.includes('min_video')">
        <btnMinVideo @callback="cbBtn" />
      </div> -->
      <div v-show="isShow && hasTextPolish && !hideIcons.includes('text_polish')">
        <btnTextPolish />
      </div>
      <div v-show="isShowSubtitle && isShow && !hideIcons.includes('subtitle')">
        <btnSubtitle />
      </div>
      <div class="hicon" v-show="isShow && !hideIcons.includes('ai_analyse') && hasTextPolish"
        @click="cbBtn('analyse', !isShowAnalyse)">
        <el-tooltip class="item" effect="dark" content="对话分析" placement="top">
          <AnalyseIcon />
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
import SelectRecord from "./SelectRecord.vue";
import AnalyseIcon from "@/app_postmeet/icons/right_header/analyse.vue";
import btnFilter from "./btnFilter/btnFilter.vue";
import btnSearch from "./btnSearch/btnSearch.vue";
import btnRereg from "./btnRereg/btnRereg.vue";
import btnMinVideo from "./btnMinVideo/btnMinVideo.vue";
import btnTextPolish from "./btnTextPolish/textPolish.vue";
import btnSubtitle from "./btnSubtitle/btnSubtitle.vue";
import btnClipper from "./btnClipper.vue";
import { getClipPermission } from "@/app_postmeet/tools/api.js";

export default {
  components: {
    AnalyseIcon,
    btnFilter,
    btnSearch,
    SelectRecord,
    btnRereg,
    btnMinVideo,
    btnTextPolish,
    btnSubtitle,
    btnClipper,
  },
  props: {
    hideIcons: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isShowClipBtn: false,
      isShow: true,
      isShowAnalyse: false,
      isShowSubtitle: false,
      hasTextPolish: false,
      hasClipPermission: false,
      // isShare: location.href.indexOf("/share/") > -1,
      hasCheckClipPermission: false,
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_store", () => {
      this.init();
      this._update();
    });
    g.emitter.on("playing_video", (value) => {
      this.isShowSubtitle = value;
      this._update();
    });
    g.emitter.on("update_show_analyse", (data) => {
      this.isShowAnalyse = data;
      this.$emit("callback", "analyse", this.isShowAnalyse);
    });

    g.emitter.on("setAnalyseTag", (list) => {
      const recordViewPermission = g.postmeetStore.getRecordViewPermission();
      if (!recordViewPermission) {
        return;
      }
      this.cbBtn("analyse", list && list.length > 0);
    });
    this._update();
  },
  methods: {
    async init() {
      const user = g.appStore?.user?.token || "";
      if (user) {
        g.cacheStore.getUserMenu("admin").then(() => {
          this._update();
        });
      }
    },
    async _update() {
      this.isHost = !g.postmeetStore.isReadonly();
      const isClip = this.hideIcons.includes("clip");
      const isMp4 = isClip
        ? false
        : g.postmeetStore.getCurrRecord().format.toLowerCase() == "mp4";

      this.hasTextPolish = g.postmeetStore.data.txtpolishs.length > 0;
      if (!this.hasTextPolish) {
        localStorage.setItem("isPlayVideo", 'true');
        g.postmeetStore.setValue("isPlayVideo", 'true');
      }
      const isShowVideoPlayer = localStorage.getItem("isPlayVideo") == "true";
      this.isShowSubtitle = isMp4 && isShowVideoPlayer;

      await this.CheckClipPermission();
      const reportSuccess = g.postmeetStore.data.saleReportStatus === "SUCCESS";
      this.isShowClipBtn =
        !isClip &&
        reportSuccess &&
        this.hasClipPermission;
    },
    async CheckClipPermission() {
      const isHaveToken = g.appStore?.user?.token || "";
      if (!isHaveToken || this.hasCheckClipPermission || !g.postmeetStore.data.confId) {
        return;
      }
      const res = await getClipPermission(g.postmeetStore.data.confId);
      this.hasClipPermission = res.data;
      this.hasCheckClipPermission = true;
    },
    cbBtn(action, data) {
      switch (action) {
        case "search":
          this.isShow = data;
          break;
        case "analyse":
          this.isShowAnalyse = data;
          this.$emit("callback", "analyse", this.isShowAnalyse);
          break;
        case "playing_video":
          this.$emit("callback", action, data);
          break;
      }
    },
  },
};
</script>

<style lang="scss">
.right_header_wrap {
  position: sticky;
  top: 0;
  height: 42px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  z-index: 10;
  background: #f7f8fc;
  padding-top: 20px;

  .rh_title {
    height: 24px;
    font-weight: 500;
    font-size: 16px;
    color: #262626;
    line-height: 24px;
  }

  .rh_right {
    display: flex;
    flex-direction: row;

    .hicon {
      color: #262626;
      margin: 0 12px;
      cursor: pointer;
    }

    .hicon:hover {
      color: #436bff;
    }
  }
}
</style>
