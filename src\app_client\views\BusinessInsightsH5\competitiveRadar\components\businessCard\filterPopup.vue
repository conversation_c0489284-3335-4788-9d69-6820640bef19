<template>
    <van-action-sheet v-model:show="actionSheetShow" title="筛选" class="action-sheet" @closed="onClose">
        <div class="div1">
            <p><span class="p1">客户</span> <span class="arrow-icon" @click="onOpenSearch">
                    <span class="single-line-ellipsis span1"> {{ customerLabel ||
                        '请选择' }} </span>
                    <van-icon name="arrow" /></span></p>
        </div>
        <div class="div2">
            <div>
                <p class="p1">情感</p>
                <div class="option-list">
                    <div v-for="(item) in emotionList" :key="item.value"
                        :class="['option-btn', { active: attitudeType === item.value }]"
                        @click="handleSelectEmotion(item)">
                        {{ item.label }}
                    </div>
                </div>
            </div>
        </div>

        <div class="action-sheet-footer">
            <van-button @click="onReset">
                重置
            </van-button>
            <van-button type="primary" block @click="onConfirm">
                确认
            </van-button>
        </div>
    </van-action-sheet>
    <search ref="searchRef" @callback="onSelectCustomer"></search>

</template>
<script setup>
import search from './search.vue';
const emit = defineEmits(['close'])
const emotionList = [
    {
        value: '0',
        label: '积极态度'
    }, {
        value: '2',
        label: '消极态度'
    }, {
        value: '1',
        label: '中性态度'
    }
]
// 0:积极 1:中性 2:消极
const attitudeType = ref('')
const customerIds = ref([])
const actionSheetShow = ref(false);
const searchRef = ref(null)
const customerLabel = ref('')
const localSelectCustomers = ref([])

const onSelectCustomer = (items) => {
    localSelectCustomers.value = items;
    customerIds.value = items.map(x => x.customerId);
    customerLabel.value = items.map(x => x.customerName).join(',');
}
// const customerLabel = computed(() => [...customerList.value].filter(i => customerIds.value.includes(i.customerId)).map(i => i.customerName).join(','))
const onOpenSearch = () => {
    nextTick(() => {
        searchRef.value.init()
    })
}
const handleSelectEmotion = (item) => {
    attitudeType.value = item.value
}
const updateCustomer = async () => {
    g.clientBiStore.setCrFilterCondition({
        customerIds: customerIds.value,
    })
}
const onReset = () => {
    customerLabel.value = ''
    attitudeType.value = ''
    customerIds.value = []
}

const onConfirm = () => {
    g.clientBiStore.setCrFilterCondition({
        attitudeType: attitudeType.value,
        customerIds: customerIds.value,
    })
    onClose()
    actionSheetShow.value = false;
}

const onClose = () => {
    emit('close',
        attitudeType.value || (customerIds.value && customerIds.value.length)
    )
}

const init = () => {
    actionSheetShow.value = true
}
watch(() => g.clientBiStore.crFbCompetitorId, () => {
    customerLabel.value = ''
})

defineExpose({
    init
})
</script>
<style lang="scss" scoped>
.action-sheet {
    p {
        margin: 0;
    }

    .div1 {
        padding: 20px;
        font-size: 12px;
        border-bottom: 1px solid #E9E9E9;

        >p {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .arrow-icon {
                color: #8C8C8C;
                display: flex;

                align-items: center;
            }

            .span1 {
                display: inline-block;
                width: 150px;
                text-align: right;
            }
        }
    }

    .div2 {
        padding: 20px;

        .option-list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 8px;
            margin: 8px 0;

            .option-btn {
                padding: 5px 12px;
                background: #F4F5F5;
                border-radius: 4px;

                font-size: 12px;
                color: #595959;
                cursor: pointer;
                transition: all 0.2s;
                word-break: keep-all;


                &.active {
                    background: #F0F3FF;
                    color: #436BFF;

                }
            }
        }




    }

    .p1 {
        font-weight: 700;
        font-size: 12px;
        color: #262626;
        line-height: 18px;
    }

    .action-sheet-footer {
        padding: 20px 15px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-top: 1px solid #E9E9E9;

        button {
            width: calc(50% - 12px);
        }
    }
}
</style>