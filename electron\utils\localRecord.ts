import * as fs from 'fs';
import * as path from 'path';
import { now } from './tools';
import { shell, app } from 'electron'

let recordingPath: string = '';
let audioBuffer: Buffer[] = [];
let accumulatedBuffer: Buffer[] = [];
let filePath = ''
let writeQueue: Buffer[] = []; // 写入队列
let isWriting = false; // 写入状态标志
let writeTimer: NodeJS.Timeout | null = null; // 写入定时器
let lastWriteTime = 0; // 上次写入时间

// 批量写入文件
const batchWriteToFile = async () => {
    if (isWriting || writeQueue.length === 0) return;

    isWriting = true;
    try {
        const batchData = Buffer.concat(writeQueue);
        writeQueue = []; // 清空队列

        // 使用异步写入，避免阻塞主进程
        await fs.promises.appendFile(filePath, batchData);

        // 记录写入操作（用于性能监控）
        console.log(`Batch write completed: ${batchData.length} bytes`);
        lastWriteTime = Date.now();
    } catch (error) {
        console.error('Error writing audio batch:', error);
    } finally {
        isWriting = false;

        // 如果队列中还有数据，继续写入
        if (writeQueue.length > 0) {
            setTimeout(batchWriteToFile, 10);
        }
    }
};

// 调度写入操作
const scheduleWrite = () => {
    if (writeTimer) {
        clearTimeout(writeTimer);
    }

    // 每300ms批量写入一次，减少磁盘I/O但保证数据不丢失
    writeTimer = setTimeout(() => {
        batchWriteToFile();
        writeTimer = null;
    }, 300);
};

// 强制写入所有待处理数据
const forceWriteAllData = async () => {
    // 先处理累积缓冲区
    if (accumulatedBuffer.length > 0) {
        const fullBuffer = Buffer.concat(accumulatedBuffer);
        writeQueue.push(fullBuffer);
        accumulatedBuffer = [];
    }

    // 调度写入
    scheduleWrite();

    // 等待写入完成
    if (isWriting || writeQueue.length > 0) {
        await new Promise(resolve => {
            const checkComplete = () => {
                if (!isWriting && writeQueue.length === 0) {
                    resolve(true);
                } else {
                    setTimeout(checkComplete, 10);
                }
            };
            checkComplete();
        });
    }
};

const _saveAudioBufferToFile = async () => {
    try {
        if (audioBuffer.length > 0) {
            // 将新的音频数据添加到累积缓冲区中
            accumulatedBuffer.push(...audioBuffer);
            audioBuffer = [];
        } else {
            return { success: false, error: 'no audio data' }
        }

        // 将累积的数据添加到写入队列
        const fullBuffer = Buffer.concat(accumulatedBuffer);
        writeQueue.push(fullBuffer);
        accumulatedBuffer = [];

        // 调度写入操作
        scheduleWrite();

        return { success: true };
    } catch (error) {
        console.error('Error saving audio chunk:', error);
        return { success: false, error };
    }
}

export const createNewRecordFile = async (data) => {
    try {
        const documentsPath = app.getPath('documents');
        recordingPath = path.join(documentsPath, 'SalesMateRecord', now('yyyyMMdd'));
        console.log(`Audio saved to ${recordingPath}`);
        if (!fs.existsSync(recordingPath)) {
            fs.mkdirSync(recordingPath, { recursive: true });
        }
        const { subject, conferenceId } = data
        const timestamp = now('hhmmss');
        const fileName = `${subject}-${conferenceId}-${timestamp}.aac`;
        filePath = path.join(recordingPath, fileName);

        // 重置缓冲区和队列
        accumulatedBuffer = [];
        writeQueue = [];
        isWriting = false;
        lastWriteTime = 0;

        if (writeTimer) {
            clearTimeout(writeTimer);
            writeTimer = null;
        }

        console.log(`Audio saved to filename:${fileName}`);
        return { success: true };
    } catch (error) {
        console.error('Error saving audio chunk:', error);
        return { success: false, error };
    }
};

export const sendAudioData = (data: ArrayBuffer) => {
    audioBuffer.push(Buffer.from(data));

    // 当缓冲区达到一定大小时才写入，减少频繁的I/O操作
    const totalBufferSize = audioBuffer.reduce((size, buffer) => size + buffer.length, 0);

    // 降低阈值到512KB，确保数据不会丢失
    if (totalBufferSize >= 512 * 1024) { // 512KB阈值
        _saveAudioBufferToFile();
    }

    return { success: true };
}

export const endLocalRecord = async () => {
    console.log('Ending local record, ensuring all data is written...');

    // 确保所有数据都写入文件
    await _saveAudioBufferToFile();

    // 强制写入所有待处理数据
    await forceWriteAllData();

    // 等待最后的写入操作完成
    if (isWriting) {
        await new Promise(resolve => {
            const checkWriting = () => {
                if (!isWriting) {
                    resolve(true);
                } else {
                    setTimeout(checkWriting, 10);
                }
            };
            checkWriting();
        });
    }

    // 清理定时器
    if (writeTimer) {
        clearTimeout(writeTimer);
        writeTimer = null;
    }

    console.log('Recording completed, opening folder:', recordingPath);
    shell.openPath(recordingPath)
    return { success: true };
}