<template>
    <div class="competitive-radar-analysis-h5">
        <div class="title-text">
            竞品分析
        </div>
        <competitorsFilter v-model="competitorId" :options="options" />
        <aiAnalysis :data="selectAIAnalysis" :loading="loadingAiAnalyse" />
        <totalCharts :loading="loading1" :data="isExpanded ? totalChartsData : totalChartsData.slice(0, 4)"
            :metrics="totalChartsMetrics" :setting="{
                title: `${competitorName}评价分布图`,
                width: 72
            }" :class="['total-charts-style', isExpanded ? 'total-charts-style-expanded' : 'total-charts-style-collapsed'
            ]">
            <template #bottom>
                <el-button link class="total-charts-bottom" @click.stop="toggleContent">
                    <span style="margin-right: 4px;">{{ isExpanded ? '收起' : '展开' }}</span>
                    <el-icon>
                        <ArrowUpBold v-if="isExpanded" />
                        <ArrowDownBold v-else />
                    </el-icon>
                </el-button>
            </template>
        </totalCharts>
        <!-- <cloudMapChart :data="cloudMapChartData" :config="{
            title: `${competitorName}词云图`
        }" @word-click="handleWordClick" class="cloud-map-chart-style" :loading="loading2">
        </cloudMapChart> -->
        <div class="details_radar">
            <p class="details_radar-title">
                {{ competitorName }}声量雷达图
                <TooltipCvCalc />
            </p>
            <radarChart :data="volumedistribution" :config="radarEchartsConfig" :loading="loading3" />

        </div>
    </div>
</template>

<script setup>
import { debounce } from "@/js/utils.js"
import { ArrowUpBold, ArrowDownBold } from '@element-plus/icons-vue'
import aiAnalysis from "../../components/aiAnalysis.vue";
import competitorsFilter from "./competitorsFilter.vue";
import totalCharts from "../../components/totalChart.vue";
import cloudMapChart from "../../components/cloudMapChart.vue";
import radarChart from "../../components/radarChart.vue";
import TooltipCvCalc from '@/app_client/components/TooltipCvCalc.vue'
// 从store获取数据
const store = g.clientBiStore

const colorObject = computed(() => {
    return g.clientBiStore.colorObject || {}
})
const totalChartsMetrics = computed(() => [{
    key: 'positiveCount',
    name: '积极',
    color: colorObject.value['positive']
}, {
    key: 'negativeCount',
    name: '消极',
    color: colorObject.value['negative']
}, {
    key: 'neutralCount',
    name: '中性',
    color: colorObject.value['neutral']
}])

const options = computed(() => store.competitorsArr.map((i) => ({
    label: i.commonName,
    value: i.competitorId,
}))) || []
const competitorId = ref('')
const competitorName = computed(() => options.value.find(i => i.value === competitorId.value)?.label || '')

const radarEchartsConfig = {
    isShowLegend: false
}
const loadingAiAnalyse = ref(true)

// 从store获取数据
const totalChartsData = computed(() => store.sentimentDistribution || [])
// const cloudMapChartData = computed(() => store.keywordSentiment || [])
const volumedistribution = computed(() => store.volumedistribution || [])
const selectAIAnalysis = computed(() => store.selectAIAnalysis)
// 是否展开内容
const isExpanded = ref(false)


// 切换内容显示状态
const toggleContent = () => {
    isExpanded.value = !isExpanded.value
}
const handleWordClick = (word) => { }
const loading1 = ref(false)
const loading2 = ref(false)
const loading3 = ref(false)

const init = async () => {
    if (!competitorId.value) return;

    const f1 = async () => {
        loading1.value = true;
        await store.getSentimentDistribution(competitorId.value);
        loading1.value = false;
    }

    // const f2 = async () => {
    //     loading2.value = true;
    //     await store.getKeywordSentiment(competitorId.value);
    //     loading2.value = false;
    // }

    const f3 = async () => {
        loading3.value = true;
        await store.getVolumedistribution(competitorId.value);
        loading3.value = false;
    }

    const f4 = async () => {
        loadingAiAnalyse.value = true;
        await store.getCompetitorAIAnalysis(competitorId.value);
        loadingAiAnalyse.value = false;
    }
    Promise.all([f1(), f3(), f4()])
}

// 创建防抖函数包装init
const debouncedInit = debounce(init, 200)

// 同时监听多个值
watch(() => [competitorId.value, g.clientBiStore.periodType], () => {
    debouncedInit()
}, { immediate: true, deep: true })

watch(() => options.value, () => competitorId.value = options.value[0]?.value || "")

</script>
<style lang="scss" scoped>
.competitive-radar-analysis-h5 {
    .total-charts-style {
        width: 100%;
        padding: 4px 16px 16px 16px;
        box-sizing: border-box;
        margin-top: 12px;

        :deep(.chart-container) {
            height: calc(100% - 70px);
        }
    }

    .total-charts-style-expanded {
        height: 420px;
    }

    .total-charts-style-collapsed {
        height: 320px;
    }

    .total-charts-bottom {
        width: 100%;
        font-size: 14px;
        color: #436BFF;
        line-height: 22px;
    }

    .cloud-map-chart-style {
        width: 100%;
        height: 360px;
        padding: 4px 16px 16px 16px !important;
        margin-top: 12px;
    }

    .details_radar {
        width: 100%;
        text-align: left;
        background: #fff;
        padding: 4px 16px 16px 16px;
        box-sizing: border-box;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
        border-radius: 12px;
        // margin-bottom: 24px;
        height: 330px;
        margin-top: 12px;

        &-title {
            font-weight: 700;
            font-size: 16px;
            color: #262626;
        }


        &-icon svg {
            background: #999;
            border-radius: 50%;
        }
    }

}
</style>