<template>

    <div class="checkSource_wrap">
        <RadioGroup v-model="competitorId" class="fch_radio" :options="options" />
        <div class="content_section flex-col">
            <FilterCondition />
            <div class="content_inner flex-row" v-loading="loading">
                <CategoryList :competitorId="competitorId" :firstCategoryId="firstCategoryId" />
                <div class="feedback_list">
                    <FeedbackList :data="competitiveSelectRadarList" />
                    <el-pagination :current-page="currentPage" :page-size="pageSize" background
                        layout="total,  prev, pager, next" :total="parseInt(totalNum)"
                        @current-change="handleCurrentChange" @size-change="handleSizeChange"
                        v-show="totalNum > pageSize" class="checkSource_wrap_pagination" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import FilterCondition from './components/FilterCondition.vue';
import CategoryList from './components/CategoryList.vue';
import FeedbackList from './components/FeedbackList.vue';
import RadioGroup from '@/app_client/components/RadioGroup.vue'
import { watch } from '@vue/reactivity';

const store = g.clientBiStore
const options = computed(() => store.competitorsArr.map((i) => ({
    label: i.commonName,
    value: i.competitorId,
})))
const competitorId = ref('')
const loading = ref(false)
const periodType = computed(() => store.periodType || '')
const currentPage = ref(1)
const pageSize = ref(4)
const totalNum = ref(0)
const crFilterCondition = computed(() => store.crFilterCondition || {})
const competitiveSelectRadarList = computed(() => {
    const listP = store.competitiveSelectRadarDataObj;
    totalNum.value = listP.totalNum;
    return listP.datas;
})
const firstCategoryId = ref('')
const customerIds = computed(() => g.clientBiStore.crFilterCondition.customerIds || [])
const attitudeType = computed(() => g.clientBiStore.crFilterCondition.attitudeType || '')
watch(() => competitorId.value, () => {
    store.crFbCompetitorId = competitorId.value
    g.clientBiStore.setCrFilterCondition({
        customerIds: '',
    })
})


const handleCurrentChange = (val) => {
    currentPage.value = val
    init()
}

const handleSizeChange = (val) => {
    pageSize.value = val
    init()
}

const init = async () => {
    loading.value = true

    if (!competitorId.value) return;
    const param = {
        pageNumber: currentPage.value,
        pageSize: pageSize.value,
        competitorId: competitorId.value,
        periodType: periodType.value,
        parentId: crFilterCondition.value.parentId,
        dimensionId: crFilterCondition.value.dimensionId,
        attitudeType: crFilterCondition.value.attitudeType,
        customerIds: customerIds.value || [],
    }
    await param.parentId && store.getCompetitorRadarData(param)
    loading.value = false
}

watch(() => options.value, (newValue) => {
    if (newValue.length > 0) {
        competitorId.value = newValue[0].value
        store.crFbCompetitorId = competitorId.value;

    }
}, { immediate: true, deep: true })

watch(() => [competitorId.value, g.clientBiStore.periodType, g.clientBiStore.crSearchVersion], async () => {
    if (!competitorId.value) return;
    const list = await store.getCrDimensionList(competitorId.value, {
        attitudeType: crFilterCondition.value.attitudeType,
        customerIds: crFilterCondition.value.customerIds || [],
    }) || []
    if (!crFilterCondition.value.parentId) {
        g.clientBiStore.setCrFilterCondition({
            parentId: firstCategoryId
        })
        firstCategoryId.value = list[0].id
    } else {
        firstCategoryId.value = crFilterCondition.value.parentId
    }

    currentPage.value = 1
    init()
}, { immediate: true, deep: true })


</script>

<style lang="scss" scoped>
.checkSource_wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .fch_radio {
        margin: 24px 0 24px 15px;
    }

    .cvh_title {
        margin: 0;
        font-weight: 700;
        font-size: 18px;
        color: #262626;
        line-height: 26px;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            display: inline-block;
            width: 3px;
            height: 14px;
            background: #436BFF;
            margin-right: 8px;
            border-radius: 2px;
        }
    }

    .filter_section {
        margin-bottom: 16px;
    }

    .content_section {
        gap: 16px;
        min-height: 0;
        padding: 24px;
        background: #F9FAFC;
        border-radius: 8px;
        margin: 0 15px !important;

        .content_inner {
            padding: 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .feedback_list {
                width: calc(100% - 240px);
            }
        }
    }

    .checkSource_wrap_pagination {
        display: flex;
        justify-content: flex-end;
        margin: 0;
        // margin: 12px 24px;
    }
}

.bottom_details_filter {
    margin: 24px 0 24px 15px;
}
</style>

<style lang="scss"></style>