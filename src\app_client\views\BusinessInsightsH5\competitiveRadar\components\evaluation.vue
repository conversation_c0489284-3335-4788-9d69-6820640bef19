<template>
    <div class="competitive-radar-evaluation-h5">
        <div class="title-text">
            竞品评价
        </div>
        <competitorsFilter v-model="competitorId" :options="options" />
        <businessCard></businessCard>

    </div>
</template>

<script setup>
import competitorsFilter from "./competitorsFilter.vue";
import businessCard from "./businessCard/businessCard.vue";
const selectCategoriesObj = computed(() => g.clientBiStore.crDrdimensionList[0] || {})
const customerIds = computed(() => g.clientBiStore.crFilterCondition.customerIds || '')
const attitudeType = computed(() => g.clientBiStore.crFilterCondition.attitudeType || '')
const store = g.clientBiStore
const options = computed(() => store.competitorsArr.map((i) => ({
    label: i.commonName,
    value: i.competitorId,
})))
const competitorId = ref('')
watch(() => options.value, () => {
    competitorId.value = options.value[0]?.value || ""
    store.crFbCompetitorId = competitorId.value;
})

watch(() => selectCategoriesObj.value, () => {
    store.setCrFilterCondition({
        parentId: selectCategoriesObj.value.id
    })
    store.crFbCompetitorId = competitorId.value;

})

watch(() => [g.clientBiStore.periodType, competitorId.value, customerIds.value, attitudeType.value], () => {
    store.getCrDimensionList(competitorId.value, {
        attitudeType: attitudeType.value,
        customerIds: customerIds.value,
    })
}, { immediate: true })

watch(() => competitorId.value, () => {
    store.crFbCompetitorId = competitorId.value
    g.clientBiStore.setCrFilterCondition({
        customerIds: '',
    })
})


</script>

<style lang="scss" scoped>
.competitive-radar-evaluation-h5 {
    background: #f7f8fa;
}
</style>