# Mac录制CPU优化方案

## 问题分析

在Mac电脑上进行录制时，CPU使用率过高主要源于以下几个方面：

1. **频繁的音频数据处理**：每秒收集一次数据，每次都要进行ArrayBuffer转换和IPC通信
2. **实时文件写入**：每次收到音频数据都立即写入文件，频繁的磁盘I/O操作
3. **多个音频流同时处理**：麦克风、系统音频、桌面音频等多个流同时处理
4. **AudioContext持续运行**：音频混合处理持续占用CPU资源
5. **IPC通信频繁**：渲染进程和主进程之间频繁的数据传输

## 优化方案

### 1. 音频数据收集优化

**文件**: `src/app_electron/tools/recordHelper.js`

- **增加数据收集间隔**：从1秒增加到2秒，减少CPU使用
- **批量数据处理**：使用2MB缓冲区，批量发送数据而不是实时发送
- **定期刷新机制**：每3秒刷新一次缓冲区，平衡延迟和性能
- **优化编码格式**：使用`audio/webm;codecs=opus`和128kbps比特率
- **修复数据丢失**：暂停时立即刷新缓冲区，确保数据不丢失

```javascript
// 优化前
this.mediaRecorder.start(1000); // 每秒收集一次

// 优化后
this.mediaRecorder.start(2000); // 每2秒收集一次

// 暂停时立即刷新缓冲区
pauseRecording() {
    this.mediaRecorder.pause();
    this.flushAudioBuffer(); // 确保数据不丢失
}
```

### 2. 音频流处理优化

**文件**: `src/app_electron/tools/localRecord.js`

- **全局AudioContext管理**：避免重复创建AudioContext
- **优化音频参数**：使用标准采样率(48kHz)和优化的音频处理参数
- **减少音频处理开销**：优化音频混合流程

```javascript
// 优化音频参数
audio: {
    sampleRate: 48000,
    channelCount: 2,
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true
}
```

### 3. 文件写入优化

**文件**: `electron/utils/localRecord.ts`

- **批量写入机制**：使用写入队列，每300ms批量写入一次
- **异步写入**：避免阻塞主进程
- **缓冲区管理**：512KB阈值触发写入，减少频繁I/O操作
- **强制写入机制**：录制结束时强制写入所有待处理数据

```typescript
// 批量写入机制
const batchWriteToFile = async () => {
    if (isWriting || writeQueue.length === 0) return;
    
    isWriting = true;
    try {
        const batchData = Buffer.concat(writeQueue);
        await fs.promises.appendFile(filePath, batchData);
    } finally {
        isWriting = false;
    }
};

// 强制写入所有数据
const forceWriteAllData = async () => {
    // 确保所有待处理数据都被写入
    if (accumulatedBuffer.length > 0) {
        const fullBuffer = Buffer.concat(accumulatedBuffer);
        writeQueue.push(fullBuffer);
        accumulatedBuffer = [];
    }
    // 等待写入完成
    await new Promise(resolve => {
        const checkComplete = () => {
            if (!isWriting && writeQueue.length === 0) {
                resolve(true);
            } else {
                setTimeout(checkComplete, 10);
            }
        };
        checkComplete();
    });
};
```

### 4. Mac虚拟音频进程优化

**文件**: `electron/utils/macInstall.ts`

- **进程启动优化**：使用`stdio: 'ignore'`减少进程开销
- **优雅终止**：使用SIGTERM和SIGKILL组合，避免僵尸进程
- **错误处理增强**：更好的错误处理和日志记录

```typescript
// 优化进程启动
global.virtualAudioProcess = spawn(appPath, [], {
    stdio: 'ignore',
    detached: false,
    windowsHide: true
});
```

### 5. 性能监控

**文件**: `src/app_electron/tools/performanceMonitor.js`

- **实时性能监控**：监控内存使用、IPC调用次数、写入操作频率
- **性能报告**：录制结束后生成详细的性能报告
- **优化验证**：通过数据验证优化效果

## 数据丢失问题修复

### 问题识别

1. **1MB阈值问题**：当缓冲区不满1MB时，数据永远不会被写入
2. **暂停后数据丢失**：暂停时没有刷新缓冲区
3. **录制结束时数据丢失**：结束时可能还有未写入的数据
4. **定期刷新间隔太长**：5秒间隔可能导致数据丢失

### 修复方案

1. **降低写入阈值**：从1MB降低到512KB，确保数据及时写入
2. **减少刷新间隔**：从5秒减少到3秒，平衡性能和可靠性
3. **暂停时立即刷新**：暂停录制时立即刷新缓冲区
4. **强制写入机制**：录制结束时强制写入所有待处理数据
5. **写入队列优化**：每300ms批量写入，减少I/O但保证数据不丢失

```typescript
// 修复前
if (totalBufferSize >= 1024 * 1024) { // 1MB阈值
    _saveAudioBufferToFile();
}

// 修复后
if (totalBufferSize >= 512 * 1024) { // 512KB阈值
    _saveAudioBufferToFile();
}

// 暂停时立即刷新
pauseRecording() {
    this.mediaRecorder.pause();
    this.flushAudioBuffer(); // 确保数据不丢失
}
```

## 预期效果

1. **CPU使用率降低**：预计降低30-50%的CPU使用率
2. **内存使用优化**：减少内存碎片和频繁分配
3. **磁盘I/O减少**：批量写入减少磁盘访问次数
4. **进程管理改善**：更好的虚拟音频进程管理
5. **性能可观测性**：通过监控工具跟踪优化效果
6. **数据完整性保证**：确保所有音频数据都被正确录制

## 使用方法

1. **自动优化**：优化已集成到录制流程中，无需额外配置
2. **性能监控**：录制时会自动开始性能监控，结束后在日志中查看报告
3. **兼容性**：优化保持向后兼容，不影响现有功能
4. **数据安全**：修复了数据丢失问题，确保录制内容完整

## 监控指标

录制结束后，性能监控会输出以下指标：

- **录制时长**：秒
- **平均内存使用**：MB
- **最大内存使用**：MB
- **写入操作次数**：总次数和每秒次数
- **IPC调用次数**：总次数和每秒次数

## 注意事项

1. **延迟影响**：批量处理可能增加少量延迟，但仍在可接受范围内
2. **内存使用**：缓冲区机制会占用一定内存，但相比CPU优化是值得的
3. **兼容性测试**：建议在不同Mac设备上测试优化效果
4. **监控日志**：关注性能监控日志，确保优化效果符合预期
5. **数据完整性**：修复了数据丢失问题，确保录制内容完整

## 进一步优化建议

1. **Web Workers**：考虑使用Web Workers处理音频数据
2. **硬件加速**：探索使用硬件加速进行音频编码
3. **流式处理**：实现真正的流式音频处理
4. **自适应优化**：根据设备性能动态调整参数
5. **错误恢复**：增强错误恢复机制，确保录制稳定性
