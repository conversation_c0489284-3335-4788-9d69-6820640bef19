<template>
    <div class="customer-voice-feedback-h5">
        <div class="title-text">
            客户反馈
        </div>
        <businessCard />
    </div>
</template>
<script setup>
import businessCard from "./businessCard/businessCard.vue";
const selectCategoriesObj = computed(() => g.clientBiStore.cvDimensionList[0] || {})
const customer = computed(() => g.clientBiStore.filterConditionObj.customer || '')
const emotion = computed(() => g.clientBiStore.filterConditionObj.emotion || '')


console.log(g.clientBiStore.filterConditionObj)

const getDimensionList = () => {
    g.clientBiStore.getCvDimensionList()
}


watch(() => selectCategoriesObj.value, (newVal) => {
    g.clientBiStore.setFilterCondition({
        levelSelect: newVal.id,
    })
})


watch(() => [g.clientBiStore.periodType, emotion.value, customer.value], () => {
    getDimensionList()
}, { immediate: true })

onMounted(() => {
    getDimensionList()

})
</script>
<style lang="scss" scoped>
.business-card {
    margin-top: 16px;
}
</style>