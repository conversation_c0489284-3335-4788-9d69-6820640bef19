import { ipcMain, dialog, desktopCapturer } from 'electron'
import { shell, app } from 'electron'
import windowManager from './windowManager'
import { createSubWindows } from './listenSubWindow'
import { ffmpegFixDuration } from '../utils/record'
import { setStore, getUserInfo, keyUserInfo, removeStore, keyDevToolsOpen, keyWindowSizes, getStore } from '../utils/store'
import { ensurePermissions, PermissionType } from '../utils/systemPermisions'
import { writeLog, uploadLog, uploadOldLogs } from '../utils/errorWriteLocal'
import { checkMacVirtualAudio, installMacVirtualAudio, killVirtualAudio } from '../utils/macInstall'
import config from '../utils/config'
import * as fs from 'fs'
import * as path from 'path'
import { startMainWindow } from './createMainWindow'
import { errorWriteLocal } from '../utils/errorWriteLocal'
import { downloadAndCacheImage } from '../utils/request'
import { getMainScreenInfo } from '../utils/tools'
import { WebResourceManager } from '../utils/webResource'
import { sendAudioData, createNewRecordFile, endLocalRecord } from '../utils/localRecord'

export const listenIpcMain = () => {
    ipcMain.on('ssoLogin', (_, res) => {
        console.log('ssoLogin', res)
        if (res.code == 0) {
            setStore(keyUserInfo, res.data)
            windowManager.closeAllWindows();
            startMainWindow();
        } else {
            console.log('ssoLogin error', res)
        }
    })

    ipcMain.handle('mainPageReady', (event) => {
        const win = windowManager.getWindow('main')
        if (win && win.window && !win.window.isDestroyed()) {
            const userInfo = getUserInfo();
            return { code: 0, data: userInfo }
        } else {
            console.log('mainPageReady win is destroyed')
            return { code: 1, data: null }
        }
    })

    ipcMain.on('forward_message', (_, data) => {
        console.log('forward_message', data)
        windowManager.sendMessage(data)
    })

    ipcMain.on('setStore', (_, key, value) => {
        setStore(key, value);
    })

    ipcMain.on('removeStore', (_, key) => {
        removeStore(key);
    })

    ipcMain.handle('getStore', (_, key) => {
        const data = getStore(key);
        return JSON.stringify(data);
    })

    ipcMain.on('updateWindowSize', (_, sizes) => {
        setStore(keyWindowSizes, sizes)
    })

    ipcMain.handle('choose-local-recording-path', async (event) => {
        const result = await dialog.showOpenDialog({
            properties: ['openDirectory']
        })

        if (result.canceled) {
            return null;
        } else {
            return result.filePaths[0]
        }
    })

    ipcMain.on('open-local-recording-path', (_, path) => {
        console.log("open-local-recording-path", path)
        shell.openPath(path)
    })

    ipcMain.on('hide_window', (_, winName) => {
        console.log('hide_window', winName)
        windowManager.hideWindow(winName)
    })

    ipcMain.on('close_window', (_, winName) => {
        console.log('close_window', winName)
        windowManager.closeWindow(winName)
    })

    ipcMain.on('create_window', (_, arg) => {
        console.log('create_window', arg)
        createSubWindows(arg)
    })

    ipcMain.on('close_all_windows', () => {
        console.log('close_all_windows')
        windowManager.closeAllWindows()
    })

    ipcMain.on('minimize_window', (_, winName) => {
        console.log('minimize_window', winName)
        windowManager.minimizeWindow(winName)
    })

    ipcMain.on('open_main', () => {
        console.log('open_main')
        startMainWindow()
    })

    ipcMain.on('update_closeable', (_, winName, closeable) => {
        console.log('update_closeable', winName, closeable)
        windowManager.updateCloseable(winName, closeable)
    })

    ipcMain.handle('ensurePermissions', async (_, type: PermissionType) => {
        errorWriteLocal('ensurePermissions', type)
        return ensurePermissions(type);
    })

    ipcMain.handle('get_desktopCapturer_sources', async () => {
        errorWriteLocal('get_desktopCapturer_sources')
        return await desktopCapturer.getSources({ types: ['screen'] })
    })

    ipcMain.on('process-recording', async (_, videoPath, isVideo, confId) => {
        errorWriteLocal('process-recording', videoPath, isVideo, confId)
        try {
            // 调用FFmpeg处理录制文件，修复时间戳问题
            const result = await ffmpegFixDuration(videoPath, isVideo, confId)
            errorWriteLocal('process-recording completed:', result)
        } catch (error) {
            errorWriteLocal('process-recording failed:', error)
        }
    })

    ipcMain.handle('check-recording-path', async (_, recordPath) => {
        try {
            if (!recordPath) {
                const documentsPath = app.getPath('documents');
                recordPath = path.join(documentsPath, 'SalesMateRecord');
            }

            if (!fs.existsSync(recordPath)) {
                fs.mkdirSync(recordPath, { recursive: true });
            }

            return recordPath;
        } catch (error) {
            console.error('Error checking/creating recording path:', error);
            return '';
        }
    })

    ipcMain.handle('check-mac-virtual-audio', async () => {
        errorWriteLocal('check-mac-virtual-audio')
        return await checkMacVirtualAudio()
    })

    ipcMain.handle('install-mac-virtual-audio', async () => {
        errorWriteLocal('install-mac-virtual-audio')
        return await installMacVirtualAudio()
    })

    ipcMain.on('kill-virtual-audio', () => {
        killVirtualAudio()
    })

    ipcMain.on('write-elog', (_, logId, ...args) => {
        console.log('write-elog', logId, args)
        if (!logId) return;
        writeLog(logId, ...args)
    })

    ipcMain.on('upload-log', (_, logId) => {
        const userInfo: any = getUserInfo()
        uploadLog(logId, userInfo.id)
    })

    ipcMain.handle('toggle_dev_tools', () => {
        const isOpen = getStore(keyDevToolsOpen, false);
        setStore(keyDevToolsOpen, !isOpen)
        return !isOpen
    })

    ipcMain.handle('get_app_version', () => {
        return config.version
    })

    ipcMain.on('open_url', (_, url) => {
        shell.openExternal(url)
    })

    ipcMain.on('update_user_agent', (_, winName, userAgent) => {
        windowManager.updateUserAgent(winName, userAgent)
    })

    ipcMain.handle('get-image-cache', async (_, imageUrl) => {
        return await downloadAndCacheImage(imageUrl);
    });

    ipcMain.handle('get-all-window-names', async () => {
        return windowManager.getAllWindowNames()
    })

    ipcMain.handle("execute_win_method", async (_, { id, method, args } = {}) => {
        try {
            errorWriteLocal('execute win', id, method, args);
            const win = windowManager.getWindow(id);
            const result = await win.window[method](...args);
            return { success: true, data: result };
        } catch (error) {
            errorWriteLocal('execute win error', error);
            return { success: false, error: error.message };
        }
    })

    ipcMain.handle('get-main-screen-info', async () => {
        return getMainScreenInfo()
    })

    ipcMain.on("update_old_logs", () => {
        uploadOldLogs()
    })

    ipcMain.on("check_hot_update", () => {
        const webResourceManager = WebResourceManager.getInstance()
        const win = windowManager.getWindow('main');
        if (!win || win.window.isDestroyed()) return;
        webResourceManager.checkAndApplyHotUpdate(win.window)
    })


    ipcMain.handle('send-audio-data', (event, data: ArrayBuffer) => {
        return sendAudioData(data);
    })

    ipcMain.handle('create_new_record_file', async (event, data) => {
        return await createNewRecordFile(data);
    })

    ipcMain.handle('end_local_record', async () => {
        return await endLocalRecord();
    })
}
