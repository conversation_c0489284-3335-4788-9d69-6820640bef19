<template>
  <div class="requirement_table_wrap">
    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
      <template #col_belonging_dimension="{ row }">
        <span class="belonging-dimension-text">{{ row.classify }}</span>
      </template>
      <template #col_user_requirement="{ row }">
        <span class="belonging-dimension-text">{{ row.demand }}</span>
      </template>
      <template #col_suggestion="{ row }">
        {{ row.suggestion }}
      </template>
      <template #header_suggestion>
        <div class="header_source_style">
          建议 <img :src="getOssUrl('ai.png', 2)" alt="" />
        </div>
      </template>
      <template #col_number_of_mentions="{ row }">
        {{ row.similaDemandAmount }}
      </template>
    </MyTable>
  </div>
  <DetailDrawer ref="detailDrawer"></DetailDrawer>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import DetailDrawer from "./detailDrawer.vue";
import { getOssUrl } from "@/js/utils.js";
const refTable = ref();
const detailDrawer = ref();

const getTableData = async (param) => {
  const resp = await g.clientBiStore.getReqTabelData(param);
  return resp;
};
const columns = [
  "user_requirement",
  "belonging_dimension",
  "number_of_mentions",
  "suggestion",
]
const datas = reactive({
  tableid: "requirement_analysis_table",
  param: {},
  need_header: true,
  show_search: false,
  show_btn_column: false,
  need_init_load: false,
  modal_type: "link",
  columns,
  template: columns,
  template_header: ["suggestion"],
  // sortables: ["priority"],
  show_overflow_tooltip: true,
  sortable: true,
  show_link_column: true,
  show_link_view: true,
  view_txt: "查看来源",
  column_widths: {
    number_of_mentions: 88,
    belonging_dimension: 120,
  },
  urlGet: getTableData,
});

const cbDatas = (action, data) => {
  // 处理表格回调事件
  if (action === "init_view") {
    detailDrawer.value.show(data);
  }
};

const init = async () => {
  if (refTable.value) {
    refTable.value.search();
  }
}

watch(() => g.clientBiStore.periodType, () => {
  init()
}, { immediate: true })

onMounted(async () => {
  init()
});

defineExpose({
  refTable,
  datas,
  cbDatas,
});
</script>

<style lang="scss" scoped>
.requirement_table_wrap {
  margin-top: 24px;
  background: #fff;
  border-radius: 8px;

  .el-select {
    width: 160px;
    margin-right: 16px;
  }

  .belonging-dimension-text {
    padding-right: 4px;

  }
}

.header_source_style {
  display: flex;
  align-items: center;

  img {
    margin-left: 4px;
    width: 16px;
    height: 16px;
  }

  &.header_source_style {
    img {
      width: 22px;
      height: 14px;
      position: absolute;
      top: 2px;
      left: 44px;
    }
  }
}

.priority-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  min-width: 40px;
}

.priority-high {
  background-color: #FF111D;
  color: white;
}

.priority-medium {
  background-color: #FF761D;
  color: white;
}

.priority-low {
  background-color: #F6BD16;
  color: white;
}
</style>
