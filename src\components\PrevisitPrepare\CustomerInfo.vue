<template>
  <div class="customer-info prepare_wrap" v-ai-tip="'bottom-right'">
    <PrepareHeader title="客户信息" @callback="onDetail" :show_btn="isShowCompanyDetail" />
    <div class="info-content">
      <div class="company-intro">
        <div class="section-title">公司简介</div>
        <div class="intro-text">{{ customerInfo.introduce }}</div>
      </div>
      <div class="company-news" v-if="newsList.length > 0">
        <div class="section-title">企业动态</div>
        <div class="news-list">
          <div class="news-item" v-for="(item, index) in newsList" :key="index">
            <div class="news-content">
              <div class="news-title" @click="handleNewsClick(item.url)">
                {{ item.title }}
                <span class="news-tag" v-if="item.isNew">NEW</span>
              </div>
              <div class="news-info">
                <span class="news-date">{{ item.date }}</span>
                <span class="news-type">{{ item.type }}</span>
              </div>
            </div>
            <div class="news-status">{{ item.status }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { jsOpenNewWindow, getFuncStatus } from "@/js/utils";

const customerInfo = ref({
  introduce: "",
  news: [],
});
const emit = defineEmits(["callback"]);

const isShowCompanyDetail = ref(false);

const onDetail = () => {
  emit("callback", "show_customer", "companyInfo");
};

const newsList = ref([]);

const handleNewsClick = (url) => {
  jsOpenNewWindow(url);
};

const init = async (data) => {
  customerInfo.value = data;
  if (data.news) {
    newsList.value = data.news
      .map((item) => ({
        title: item.Title,
        date: item.PublishTime.split(" ")[0],
        type: item.Source,
        url: item.Url,
        status: item.EmotionType === "positive" ? "积极" : "中立",
        isNew: new Date(item.PublishTime) > new Date(Date.now() - 24 * 60 * 60 * 1000),
        publishTime: new Date(item.PublishTime),
      }))
      .sort((a, b) => b.publishTime - a.publishTime)
      .slice(0, 5);
  }
  isShowCompanyDetail.value = getFuncStatus("sales_company_in_stategy");
};

defineExpose({
  init,
  handleNewsClick,
  newsList,
  onDetail,
  isShowCompanyDetail,
});
</script>

<style scoped lang="scss">
.customer-info {
  .section-title {
    display: inline-block;
    padding: 2px 6px;
    background: #e6ebfd;
    border-radius: 4px;
    font-weight: 500;
    font-size: 12px;
    color: #436bff;
    line-height: 18px;
    margin-bottom: 12px;
  }

  .intro-text {
    font-size: 14px;
    color: #262626;
    line-height: 1.6;
    margin-bottom: 20px;
  }

  .company-news {
    background: #f9fafc;
    border-radius: 8px;

    .news-list {
      .news-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 12px 0;
        border-bottom: 1px solid #e5e6eb;

        &:hover {
          .news-title {
            color: #436bff;
          }
        }

        &:last-child {
          border-bottom: none;
        }

        .news-content {
          flex: 1;
          margin-right: 16px;
        }

        .news-title {
          font-size: 14px;
          color: #262626;
          margin-bottom: 8px;
          cursor: pointer;

          .news-tag {
            display: inline-block;
            padding: 2px 6px;
            background: #ff7d00;
            color: #fff;
            border-radius: 2px;
            font-size: 12px;
            margin-left: 8px;
          }
        }

        .news-info {
          font-size: 12px;
          color: #86909c;

          .news-type {
            margin-left: 16px;
          }
        }

        .news-status {
          font-size: 12px;
          color: #86909c;
          border-radius: 2px;
          border: 1px solid #d9d9d9;
          padding: 2px 4px;
        }
      }
    }
  }
}
</style>
