<template>
  <div class="audio_player_wrap">
    <div class="controls">
      <div class="play_btn" @click="onPlay">
        <PauseIcon v-if="playing" />
        <PlayIcon v-else />
      </div>
      <div class="controls-con">
        <el-slider v-model="play_slider" @change="onTimeChange" :show-tooltip="false"></el-slider>
        <div class="play_times">
          <div>{{ current_time }}</div>
          <div>{{ total_time }}</div>
        </div>
      </div>
      <el-popover placement="bottom" :width="80" trigger="click">
        <template #default>
          <div class="pop_speed_list">
            <div v-for="speed in speeds" :key="speed" @click="onChooseSpeed(speed)"
              :class="{ active: currentSpeed === speed }">
              {{ speed }}x
            </div>
          </div>
        </template>
        <template #reference>
          <div class="speed_btn ticon">倍速</div>
        </template>
      </el-popover>

      <el-popover placement="bottom" width="60" trigger="click">
        <div class="pop_speaker_slider">
          <div class="vol_name">{{ volume }}%</div>
          <el-slider v-model="volume" vertical height="100px" :min="0" :max="100" class="speaker_slider"
            :show-tooltip="false" @change="onVolumeChange">
          </el-slider>
        </div>
        <template #reference>
          <div class="speaker_icon ticon">
            <SpeakerIcon />
          </div>
        </template>
      </el-popover>

      <!-- <div class="close_icon ticon" @click="onClose">
        <CloseIcon />
      </div> -->
    </div>
  </div>
</template>

<script>
import PlayIcon from "@/icons/play.vue";
import PauseIcon from "@/icons/pause.vue";
import SpeakerIcon from "./icons/speaker.vue";
import CloseIcon from "./icons/close.vue";
import { formatDuration } from "@/js/utils.js";
export default {
  components: { SpeakerIcon, PlayIcon, PauseIcon, CloseIcon },
  data() {
    return {
      playing: false,
      play_slider: 0,
      volume: 86,
      current_time: "",
      total_time: "",
      activeName: "summary",
      duration: 0,
      speeds: [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
      currentSpeed: 1,
    };
  },
  mounted() {
    this.initAudioListener();
  },
  methods: {
    init(duration) {
      console.log("init audio", duration);
      this._initTime(duration);
      g.emitter.on("setPlayItem", (item) => {
        console.log("setPlayItem", item);
        this._initTime(item.duration);
      });
    },
    onClose() {
      g.emitter.emit("showVideoPlayer", true);
    },
    _initTime(duration) {
      this.current_time = this.current_time || "00:00:00";
      this.duration = duration || g.postmeetStore.getCurrRecord().duration;
      this.total_time = formatDuration(this.duration / 1000);
    },
    initAudioListener() {
      g.emitter.on("videoTimeUpdateNoStart", (bt) => {
        this.current_time = formatDuration(bt / 1000);
        this.play_slider = (100 * bt) / this.duration;
      });
      g.emitter.on("videoStatus", (status) => {
        this.playing = status == "playing";
      });
    },
    handleClick() {
      this.$emit("callback", this.activeName);
    },
    onChooseSpeed(speed) {
      this.currentSpeed = speed;
      g.emitter.emit("updateVideoSpeed", speed);
    },
    onPlay() {
      this.playing = !this.playing;
      g.emitter.emit("video_control", this.playing ? "play" : "pause");
    },
    onTimeChange(value) {
      if (this.current_time != "00:00:00") {
        this.playing = false;
        g.emitter.emit("video_control", "pause");
        const drag_second = parseInt((value * this.duration) / 1000 / 100);
        g.emitter.emit("updateVideoTime", drag_second);
      }
    },
    onVolumeChange() {
      g.emitter.emit("updateVideoVolume", this.volume / 100);
    },
  },
};
</script>

<style lang="scss">
.audio_player_wrap {
  position: sticky;
  bottom: 0;
  height: 66px;
  width: 86%;
  background: #fff;
  box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding-left: 32px;
  z-index: 2;

  .controls {
    display: flex;
    flex-direction: row;
    width: 97%;
    align-items: center;
    gap: 12px;

    .play_btn {
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    .controls-con {
      width: 75%;
      margin: 6px 24px 0 12px;

      .el-slider {
        padding-left: 0;
        margin-right: 8px;
      }

      .play_times {
        display: flex;
        justify-content: space-between;
        margin-top: -7px;
        padding: 0 2px;

        div {
          height: 18px;
          font-size: 12px;
          color: #8c8c8c;
          line-height: 18px;
        }
      }
    }

    .speed_btn {
      width: 36px;
      height: 22px;
      font-size: 14px;
      line-height: 22px;
      margin: 7px 12px 7px 0;
    }

    .speaker_icon {
      margin: 0 12px;
      padding-top: 0;
      display: flex;
      align-items: center;
    }

    .close_icon {
      margin-left: 12px;
      display: flex;
      align-items: center;
    }

    span {
      margin-top: -5px;
    }

    .ticon {
      color: #595959;
      cursor: pointer;
    }

    .ticon:hover {
      color: #436bff;
    }
  }
}

.pop_speed_list {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  align-items: center;

  div {
    width: 100%;
    padding: 8px 0;
    cursor: pointer;
    text-align: center;
    color: #595959;
    font-size: 14px;
    transition: all 0.2s;

    &:hover {
      color: #436bff;
      background-color: #f5f7ff;
    }

    &.active {
      color: #436bff;
      background-color: #f5f7ff;
    }
  }
}

.pop_speaker_slider {
  width: 40px;
  height: 140px;
  background: #ffffff;
  border-radius: 4px;
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  align-items: center;

  .vol_name {
    height: 20px;
    font-size: 12px;
    color: #595959;
    line-height: 20px;
    text-align: center;
    margin-bottom: 4px;
  }

  .speaker_slider {
    margin: 0;
    padding: 0 8px;
  }
}
</style>
