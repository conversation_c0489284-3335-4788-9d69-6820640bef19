<template>
    <van-popup v-model:show="showMenu" round position="bottom" :close-on-click-overlay="false"
        :style="{ height: '80%' }" closeable class="dimension-popup" teleport="body">
        <div class="title">
            维度
        </div>
        <div class="menu-container">
            <van-radio-group v-model="activeIndex" icon-size="16px" shape="dot">
                <van-cell-group>
                    <van-cell :title="item.name" clickable @click="handleLeftMenuClick(item, idx)"
                        v-for="(item, idx) in categoriesList">
                        <template #right-icon>
                            <span class="count-text">{{ item.cnt }}条</span>
                        </template>
                        <template #icon>
                            <span class="radio-icon"> <van-radio :name="idx">
                                    <template #icon="props">
                                        <img v-if="props.checked" :src="getOssUrl('radio-icon.png', 3)" alt=""
                                            class="radio-icon-img">
                                    </template>
                                </van-radio></span>

                        </template>
                    </van-cell>
                </van-cell-group>
            </van-radio-group>
            <!-- 左侧一级菜单 -->
            <!-- <div class="menu-left">
                <div v-for="(item, idx) in categoriesList" :key="item.name"
                    :class="['menu-left-item', { active: idx === activeIndex }]"
                    @click="handleLeftMenuClick(item, idx)">
                    {{ item.name }}
                </div>
            </div> -->
            <!-- 右侧二级菜单 -->
            <!-- <div class="menu-right" v-if="activeIndex !== null">
                <div v-for="(sub, subIdx) in categoriesList[activeIndex || 0].children" :key="subIdx"
                    class="menu-right-row" @click="handleOptionClick(categoriesList[activeIndex || 0], sub, subIdx)">
                    <div class="menu-right-options">
                        <span class="menu-right-option" :class="{ checked: subIdx === activeIndexLast }"> {{ sub.name
                            }}</span>
                        <span v-if="subIdx === activeIndexLast" class="check-icon">✔</span>
                    </div>
                </div>
            </div> -->
        </div>
        <div class="action-sheet-footer">
            <!-- <van-button @click="onReset">
                重置
            </van-button> -->
            <van-button type="primary" block @click="onConfirm">
                确认
            </van-button>
        </div>
    </van-popup>


</template>
<script setup>
import { getOssUrl } from '@/js/utils.js';
const emit = defineEmits(['close', 'success'])

const showMenu = ref(false);
const activeName = ref('')
const activeIndex = ref(null)
const activeIndexLast = ref(null)
const selectedItem = ref(null)
const level = ref('')
// 定义分类数据结构
const categoriesList = computed(() => g.clientBiStore.cvDimensionList || [])
const filterCondition = computed(() => g.clientBiStore.filterConditionObj || {})

const handleLeftMenuClick = (item, idx) => {
    selectedItem.value = item
    activeIndex.value = idx;
    level.value = item.id
    activeIndexLast.value = null
}

const handleOptionClick = (item, sub, optIdx) => {
    activeIndexLast.value = optIdx;
    activeName.value = `${item.name} - ${sub.name}`
    level.value = sub.id
}

const onReset = () => {
    activeName.value = ''
    level.value = ''
    activeIndex.value = 0
    activeIndexLast.value = null

}
const onConfirm = () => {
    g.clientBiStore.setFilterCondition({
        levelSelect: level.value,
    })
    showMenu.value = false
    emit('success', selectedItem.value)

}

const init = () => {
    showMenu.value = true
    level.value = filterCondition.value.levelSelect
    activeIndex.value = categoriesList.value.findIndex(item => item.id === level.value) || 0
    selectedItem.value = categoriesList.value[activeIndex.value]
}

watch(() => categoriesList.value, (newList) => {
    if (newList && newList.length > 0) {
        activeIndex.value = 0
    }
})

defineExpose({
    init
})


</script>
<style lang="scss" scoped>
.dimension-popup {
    .title {
        font-weight: 700;
        font-size: 16px;
        color: #262626;
        line-height: 24px;
        padding: 14px;
        text-align: center;
    }

    .menu-container {
        margin-top: 12px;
        display: flex;

        height: calc(100% - 140px);
        background: #fff;

        width: 100%;
        box-sizing: border-box;
        overflow: auto;

        .van-radio-group {
            width: 100%;
        }

        .count-text {
            font-weight: 400;
            font-size: 14px;
            color: #8C8C8C;
            line-height: 24px;
        }

        .radio-icon {
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;

            .radio-icon-img {
                width: 14px;
                height: 14px;
            }

        }
    }

    .menu-left {
        width: 120px;
        background: #f7f8fa;
        display: flex;
        flex-direction: column;

        .menu-left-item {
            padding: 18px 0 18px 24px;
            font-size: 14px;
            color: #262626;
            line-height: 20px;
            cursor: pointer;

            &.active {
                color: #2764ff;
                // background: #fff;
                font-weight: bold;
            }
        }
    }

    .menu-right {
        flex: 1;
        // padding: 12px 0 12px 14px;

        .menu-right-row {
            display: flex;
            align-items: center;
            flex-direction: row;
            // margin-bottom: 16px;

            .menu-right-options {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                width: 100%;
                box-sizing: border-box;
                padding: 14px 12px;

                .check-icon {

                    color: #2764ff;
                    font-size: 16px;
                    // border: 1px solid #2764ff;
                    border-radius: 3px;
                    width: 18px;
                    height: 18px;
                    text-align: center;
                    line-height: 18px;
                }

                .menu-right-option {
                    font-weight: 400;
                    font-size: 12px;
                    color: #595959;
                    line-height: 17px;
                    cursor: pointer;




                    &.checked {
                        color: #2764ff;
                    }

                }
            }
        }
    }

    .action-sheet-footer {
        padding: 20px 15px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-top: 1px solid #E9E9E9;

        button {
            // width: calc(50% - 12px);
        }
    }
}
</style>