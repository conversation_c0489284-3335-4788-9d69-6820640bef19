<template>
    <div class="category_list_wrap">
        <el-menu ref="menu" :default-active="activeIndex" :default-openeds="defaultOpeneds" class="category_menu"
            @select="handleSelect">
            <template v-for="category in categoriesList">
                <!-- 有子菜单的情况使用折叠菜单 
                <el-sub-menu v-if="category.children && category.children.length > 0" :key="`sub-${category.id}`"
                    :index="category.id">
                    <template #title>
                        <div class="menu-title">
                            <img :src="getOssUrl('star2.png', 2)" alt="star" class="star2" />
                            <span>{{ category.name }}</span>
                        </div>
                        <div class="item_count">{{ category.count }}</div>
                    </template>
<el-menu-item v-for="item in category.children" :key="item.id" :index="item.id">
    <div>{{ item.name }}</div>
    <div class="item_count">{{ item.count }}</div>
</el-menu-item>
</el-sub-menu>-->
                <!-- 没有子菜单的情况使用普通菜单项 -->

                <el-menu-item :index="category.id">
                    <div>
                        <img :src="getOssUrl('star2.png', 2)" alt="star" class="star2" />
                        <span class="el-menu-item-else-title">{{ category.name }}</span>
                    </div>
                    <div class="item_count">{{ category.cnt }}条</div>
                </el-menu-item>
            </template>
        </el-menu>
    </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
const props = defineProps(['type', 'competitorId', 'firstCategoryId'])
const activeIndex = ref('')
const defaultOpeneds = ref([])
const menu = ref(null)
const competitorId = computed(() => props.competitorId || '')
// 定义分类数据结构
const categoriesList = computed(() => g.clientBiStore.crDrdimensionList || [])
const firstCategoryId = computed(() => props.firstCategoryId || '')

// parentId: crFilterCondition.value.parentId, 父级菜单

// 菜单选择事件处理
const handleSelect = (index) => {
    activeIndex.value = index
    g.clientBiStore.setCrFilterCondition({
        parentId: activeIndex.value
    })
    g.clientBiStore.updateCrFbVersion()
}

// 设置默认展开第一个
const setDefaultOpenMenu = () => {
    const first = categoriesList.value.find(item => item.id == firstCategoryId.value)
    if (first) {
        const menuId = first.id;
        defaultOpeneds.value = [menuId];
        // 只有在没有当前选中项时才设置默认值
        console.log(activeIndex.value, 222)
        if (!activeIndex.value) {
            activeIndex.value = first.id
            // 设置到 store 中并触发搜索
            g.clientBiStore.setCrFilterCondition({
                parentId: activeIndex.value
            })
        }

    }

};

// 统一的默认选中处理函数
const handleDefaultSelection = () => {
    if (categoriesList.value && categoriesList.value.length > 0) {
        setDefaultOpenMenu()
    }
}

watch(() => [firstCategoryId.value, categoriesList.value], () => {
    nextTick(() => handleDefaultSelection())

}, { immediate: true, deep: true })

watch(() => [g.clientBiStore.periodType, competitorId.value], () => {
    activeIndex.value = ''
}, { deep: true, immediate: true })

</script>
