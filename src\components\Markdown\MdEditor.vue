<template>
    <el-dialog :title="title" v-model="isShow" :fullscreen="true" :append-to-body="true" :modal-append-to-body="false"
        modal-class="md_edit_wrap">
        <div class="me_main">
            <div class="me_left" v-show="isEditMode">
                <textarea v-model="markdownContent" />
            </div>
            <div class="me_right">
                <MdViewer :md="markdownContent" :copy="false" :edit="false" />
            </div>
        </div>
        <template #footer class="dialog-footer">
            <el-button type="default" @click="onCancel">关闭</el-button>
            <el-button type="primary" @click="onConfirm" v-show="isEditMode">保存</el-button>
        </template>
    </el-dialog>
</template>

<script>
import MdViewer from "./MdViewer.vue"

export default {
    data() {
        return {
            isEditMode: false,
            markdownContent: '',
            isShow: false,
            title: '编辑',
        }
    },
    components: {
        MdViewer
    },
    mounted() {
        g.emitter.on('set_md_editor_value', (value) => {
            this.isEditMode = true;
            this.markdownContent = value;
            this.isShow = true;
        })

    },
    unmounted() {
        g.emitter.off('set_md_editor_value');
    },
    methods: {
        show(value, eidtMode = true) {
            this.isEditMode = eidtMode;
            this.title = eidtMode ? '编辑' : '查看';
            this.markdownContent = value;
            this.isShow = true;
        },
        onCancel() {
            this.isShow = false
        },
        onConfirm() {
            this.$emit('callback', this.markdownContent);
        },
    }
}
</script>


<style lang='scss'>
.md_edit_wrap {
    :deep(.el-dialog__body) {
        padding: 10px 20px;
    }

    .el-dialog__footer {
        border-top: 1px solid #dcdfe6;
    }

    .me_main {
        display: flex;
        flex-direction: row;
        height: calc(100vh - 124px);

        .me_left {
            width: 50%;
            padding: 20px;
            border: 1px solid #dcdfe6;

            textarea {
                outline: none;
                width: 100%;
                height: 100%;
                border-radius: 4px;
                font-family: "Courier New", Consolas, monospace;
                font-size: 14px;
                line-height: 1.6;
                border: none;
                resize: none;
            }
        }

        .me_right {
            width: 50%;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: #fff;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }
    }
}
</style>
