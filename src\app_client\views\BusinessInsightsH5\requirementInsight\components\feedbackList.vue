<template>
    <div class="feedback-list-container">
        <van-list v-model="loading" :finished="finished" :finished-text="feedbackList.length > 0 ? '没有更多了' : ''"
            @load="onLoad">
            <div v-for="(item, idx) in feedbackList" :key="idx" class="custom-card-item">
                <div class="custom-card-item-t">
                    <span class="customer-name">{{ item.customerName }} </span>
                    <span class="detail-link" @click="goDetail(item)">详情</span>
                </div>

                <TextEllipsis :max-lines="2" class="custom-card-item-c" :text="item.original" :lineHeight="1.5">
                    {{ item.original }}
                </TextEllipsis>

                <div class="footer-info">
                    <span>创建人:{{ item.hostName }}</span>
                    <span>发言人:{{ item.feedbackName }}</span>
                    <span>{{ item.createdTime?.substring(5, 10) }}</span>
                </div>
            </div>
        </van-list>
        <el-empty v-if="isEmpty" style="display: flex;justify-content: flex-start;" :image="getOssUrl('no-data.png', 3)"
            description="暂无数据" />
    </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
import TextEllipsis from '../../components/textEllipsis.vue';
const props = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    isFinished: {
        type: Boolean,
        default: false
    },
    isLoading: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['loadMore', 'update:loading']);

const feedbackList = computed(() => props.list);
const finished = computed(() => props.isFinished);
const loading = computed({
    get: () => props.isLoading,
    set: (val) => {
        emit('update:loading', val);
    }
});
const isEmpty = computed(() => feedbackList.value.length === 0);

const formatDate = (dateString) => {
    if (!dateString) return '';
    return dateString.substring(5, 10);
};

const onLoad = () => {
    emit('loadMore');
};
const goDetail = (item) => {
    g.clientStore._openMeetRecordCustomer(item.conferenceId, 'analyse', 'attitude')
}

const isHide = ref(true)
const isShowTooltip = (val, e) => {
    isHide.value = g.appStore.isShowTooltip(val, e, 14, 2);
}
</script>

<style lang="scss" scoped>
.feedback-list-container {
    width: 100%;
    padding: 14px;
    box-sizing: border-box;
    // height: calc(100% - 144px);
    // overflow-y: auto;
}

.custom-tag {
    display: inline-block;
    background: #e6f7e6;
    color: #13c26b;
    border-radius: 2px;
    padding: 1px 8px;
    font-size: 12px;
    line-height: 22px;

    &.positive {
        color: #04CCA4;
        background: rgba(4, 204, 164, 0.1);
    }

    &.middle {
        color: #595959;
        background: #F5F5F5;
    }

    &.neutral {
        color: #FF6B3B;
        background: rgba(255, 107, 59, 0.1);
    }
}

.custom-card-item {
    background: #F9FAFC;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 16px;
    box-sizing: border-box;

    .custom-card-item-t {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        font-weight: 400;
        font-size: 14px;
        color: #8C8C8C;
        line-height: 24px;

        .customer-name {
            width: calc(100% - 36px);
        }

        .detail-link {
            color: #436BFF;
            font-size: 14px;
            line-height: 24px;
            // text-decoration: underline;
        }
    }

    .custom-card-item-c {
        font-weight: 700;
        font-size: 14px;
        color: #262626;
        line-height: 26px;
        text-align: left;
        margin: 8px 0;
    }

    .footer-info {
        display: flex;
        gap: 8px;
        font-weight: 400;
        font-size: 14px;
        color: #8C8C8C;
        line-height: 24px;
        box-sizing: border-box;
        flex-wrap: wrap;

        span {
            word-break: break-all;
            white-space: normal;
            max-width: 100%;
        }

        span:not(:last-child)::after {
            content: '';
            display: inline-block;
            width: 1px;
            height: 14px;
            background: #BFBFBF;
            margin-left: 8px;
            vertical-align: middle;
            line-height: 24px;
        }
    }
}
</style>