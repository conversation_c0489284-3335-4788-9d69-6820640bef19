import { join, dirname, basename } from 'path'
import { exec } from 'child_process'
import { app, shell } from 'electron'
import { unlink, stat } from 'fs/promises'
import { errorWriteLocal } from './errorWriteLocal'
import { uploadRecordFile } from './uploadRecordFile'
import * as ffmpeg from 'fluent-ffmpeg'

interface FfmpegConfig {
    cmd: string;
    tempOutputFileName: string;
    finalOutputFileName: string;
}

// 获取ffmpeg可执行文件路径
const getFfmpegPath = (): string => {
    let ffmpegPath: string
    if (process.env.NODE_ENV === 'development') {
        ffmpegPath = join(app.getAppPath(), `build/extraResources/${process.platform === 'win32' ? 'win/ffmpeg.exe' : 'mac/ffmpeg'}`)
    } else {
        if (process.platform === 'win32') {
            ffmpegPath = join(dirname(app.getPath('exe')), `resources/extraResources/win/ffmpeg.exe`)
        } else {
            ffmpegPath = join(dirname(app.getPath('exe')), `../Resources/build/extraResources/mac/ffmpeg`)
        }
    }
    errorWriteLocal('ffmpegPath', ffmpegPath)
    return ffmpegPath
}

// 使用fluent-ffmpeg处理音频文件
const processAudioWithFluentFfmpeg = (inputPath: string, outputPath: string): Promise<string> => {
    return new Promise((resolve, reject) => {
        const ffmpegPath = getFfmpegPath()

        // 设置ffmpeg可执行文件路径
        ffmpeg.setFfmpegPath(ffmpegPath)

        errorWriteLocal("fluent-ffmpeg processing audio:", inputPath, "->", outputPath)
        errorWriteLocal("fluent-ffmpeg using ffmpeg path:", ffmpegPath)

        // 检查输入文件是否存在
        try {
            const fs = require('fs')
            if (!fs.existsSync(inputPath)) {
                const error = new Error(`Input file does not exist: ${inputPath}`)
                errorWriteLocal('fluent-ffmpeg input file check failed:', error.message)
                reject(error)
                return
            }
            errorWriteLocal('fluent-ffmpeg input file exists, size:', fs.statSync(inputPath).size, 'bytes')
        } catch (fsError) {
            errorWriteLocal('fluent-ffmpeg file system error:', fsError)
            reject(fsError)
            return
        }

        const command = ffmpeg(inputPath)
            .audioCodec('aac')
            .audioBitrate('128k')
            .audioFrequency(48000)
            .audioChannels(2)
            .outputOptions([
                '-movflags +faststart',  // 优化流媒体播放
                '-fflags +genpts',       // 生成时间戳
                '-avoid_negative_ts make_zero', // 避免负时间戳
                '-map_metadata 0',       // 保留元数据
                '-write_xing 0'          // 禁用Xing头，有助于时间轴拖动
            ])
            .on('start', (commandLine) => {
                errorWriteLocal('fluent-ffmpeg command:', commandLine)
            })
            .on('progress', (progress) => {
                errorWriteLocal('fluent-ffmpeg progress:', progress.percent + '% done')
            })
            .on('end', () => {
                errorWriteLocal('fluent-ffmpeg processing finished successfully')
                resolve(outputPath)
            })
            .on('error', (err, stdout, stderr) => {
                errorWriteLocal('fluent-ffmpeg error details:', {
                    message: err.message,
                    stack: err.stack,
                    stdout: stdout,
                    stderr: stderr
                })
                reject(err)
            })
            .on('stderr', (stderrLine) => {
                errorWriteLocal('fluent-ffmpeg stderr:', stderrLine)
            })

        try {
            command.save(outputPath)
        } catch (saveError) {
            errorWriteLocal('fluent-ffmpeg save error:', saveError)
            reject(saveError)
        }
    })
}

// 获取ffmpeg运行配置（保留用于视频处理）
const getFfmpegConfig = (videoPath: string, isVideo: boolean): FfmpegConfig => {
    const ffmpegPath = getFfmpegPath()
    const inputFileName = basename(videoPath);
    const pre = 'novaguide_'
    let tempOutputFileName = pre + inputFileName
    let finalOutputFileName = inputFileName
    let cmd: string

    if (isVideo) {
        // 视频文件处理
        finalOutputFileName = inputFileName
        cmd = `"${ffmpegPath}" -y -i "${inputFileName}" -c copy "${tempOutputFileName}"`
    } else {
        // 音频文件处理 - 现在使用fluent-ffmpeg，这里保留作为备用
        finalOutputFileName = inputFileName.replace(pre, '').replace('.aac', '.m4a')
        cmd = `"${ffmpegPath}" -y -i "${inputFileName}" -vn -c:a aac -b:a 128k -ar 48000 -ac 2 -movflags +faststart -fflags +genpts -avoid_negative_ts make_zero "${tempOutputFileName}"`
    }

    return {
        cmd,
        tempOutputFileName,
        finalOutputFileName
    }
}

// 处理录制文件
export const ffmpegFixDuration = async (videoPath: string, isVideo: boolean, confId: string = '') => {
    try {
        errorWriteLocal("process-recording videoPath", videoPath, confId)

        // 添加文件大小检查
        const stats = await stat(videoPath)
        if (stats.size === 0) {
            errorWriteLocal("process-recording skip: file size is 0")
            await unlink(videoPath) // 删除空文件
            return null
        }

        const videoDir = dirname(videoPath)
        const inputFileName = basename(videoPath)
        const pre = 'novaguide_'

        if (isVideo) {
            // 视频文件处理 - 使用原有的exec方式
            const { cmd, finalOutputFileName } = getFfmpegConfig(videoPath, isVideo)

            return new Promise((resolve, reject) => {
                const options = {
                    cwd: videoDir
                }

                errorWriteLocal("process-recording executing command:", cmd)

                exec(cmd, options, async (error, stdout, stderr) => {
                    if (error) {
                        errorWriteLocal("process-recording error:", error)
                        errorWriteLocal("process-recording stderr:", stderr)
                        reject(error)
                        return
                    }

                    if (stdout) {
                        errorWriteLocal("process-recording stdout:", stdout)
                    }

                    try {
                        const outputPath = join(videoDir, finalOutputFileName)
                        errorWriteLocal("process-recording output path:", outputPath)

                        // 删除原文件
                        await unlink(videoPath)

                        if (confId) {
                            // 上传录制文件
                            await uploadRecordFile(confId, outputPath)
                        }

                        // 打开目录
                        shell.openPath(videoDir)

                        errorWriteLocal("process-recording success:", videoDir)
                        resolve(outputPath)
                    } catch (err) {
                        errorWriteLocal('ffmpeg process error1:', err)
                        reject(err)
                    }
                })
            })
        } else {
            // 音频文件处理 - 使用fluent-ffmpeg，失败时回退到exec方式
            const finalOutputFileName = inputFileName.replace(pre, '').replace('.aac', '.m4a')
            const outputPath = join(videoDir, finalOutputFileName)

            errorWriteLocal("process-recording using fluent-ffmpeg for audio:", videoPath, "->", outputPath)

            try {
                // 首先尝试使用fluent-ffmpeg处理音频
                await processAudioWithFluentFfmpeg(videoPath, outputPath)

                // 删除原文件
                await unlink(videoPath)

                if (confId) {
                    // 上传录制文件
                    await uploadRecordFile(confId, outputPath)
                }

                // 打开目录
                shell.openPath(videoDir)

                errorWriteLocal("process-recording success with fluent-ffmpeg:", videoDir)
                return outputPath
            } catch (fluentError) {
                errorWriteLocal('fluent-ffmpeg failed, trying fallback exec method:', fluentError)

                // 回退到原来的exec方式
                const { cmd } = getFfmpegConfig(videoPath, false)
                const tempOutputFileName = pre + inputFileName.replace('.aac', '.m4a')

                return new Promise((resolve, reject) => {
                    const options = {
                        cwd: videoDir
                    }

                    errorWriteLocal("process-recording fallback executing command:", cmd)

                    exec(cmd, options, async (error, stdout, stderr) => {
                        if (error) {
                            errorWriteLocal("process-recording fallback error:", error)
                            errorWriteLocal("process-recording fallback stderr:", stderr)
                            reject(error)
                            return
                        }

                        if (stdout) {
                            errorWriteLocal("process-recording fallback stdout:", stdout)
                        }

                        try {
                            // 重命名临时文件为最终文件名
                            const { rename } = await import('fs/promises')
                            await rename(
                                join(videoDir, tempOutputFileName),
                                outputPath
                            )

                            // 删除原文件
                            await unlink(videoPath)

                            if (confId) {
                                // 上传录制文件
                                await uploadRecordFile(confId, outputPath)
                            }

                            // 打开目录
                            shell.openPath(videoDir)

                            errorWriteLocal("process-recording fallback success:", videoDir)
                            resolve(outputPath)
                        } catch (err) {
                            errorWriteLocal('fallback process error:', err)
                            reject(err)
                        }
                    })
                })
            }
        }
    } catch (error) {
        errorWriteLocal('ffmpeg process error2:', error)
        throw error
    }
}
