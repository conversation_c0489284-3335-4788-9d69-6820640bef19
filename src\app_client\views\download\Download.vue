<template>
  <div class="downlod_wrap">
    <div class="download_page flex-col">
      <div class="box1 flex-col">
        <div class="main1 flex-col">
          <div class="main2 flex-row">
            <img class="pic1" :src="getOssUrl('logo2.svg')" @click="toPage" />
          </div>
        </div>
        <div class="main3 flex-col">
          <div class="wrap1 flex-col">
            <span class="word1">下载绚星销售助手客户端</span>
            <span class="txt1">同时支持&nbsp;iOS，Android，macOS，Windows版本</span>
            <span class="txt12">支持Chrome 89+,&nbsp;Edge 89+ 浏览器</span>
            <div class="mod1 flex-row">
              <div class="section1 flex-col" @mouseover="mouseOverIos" @mouseleave="mouseLeaveIos">
                <div class="wrap2 flex-col" v-if="!show_ios_qr">
                  <div class="layer1 flex-col">
                    <div class="outer1 flex-row">
                      <div class="outer2 flex-col"></div>
                      <div class="outer3 flex-col">
                        <img class="icon1" :src="getOssUrl('dl_1.png')" />
                      </div>
                    </div>
                    <div class="outer4 flex-row">
                      <div class="mod2 flex-col"></div>
                    </div>
                  </div>
                  <span class="info1">iOS客户端</span>
                  <span class="info4">iOS&nbsp;11.0+</span>
                </div>
                <div class="wrap2 flex-col" v-else>
                  <img class="qr" :src="getOssUrl('dl_mobile.png')" />
                  <span class="info1">扫码下载</span>
                </div>
              </div>

              <div class="section2 flex-col" @mouseover="mouseOverAndroid" @mouseleave="mouseLeaveAndroid">
                <div class="group1 flex-col" v-if="!show_android_qr">
                  <div class="section3 flex-col">
                    <div class="wrap3 flex-col">
                      <img class="icon2" :src="getOssUrl('dl_3.png')" />
                    </div>
                    <div class="wrap4 flex-col">
                      <div class="group2 flex-col">
                        <div class="section4 flex-col"></div>
                      </div>
                    </div>
                  </div>
                  <span class="txt2">Android客户端</span>
                  <span class="info5">Android&nbsp;5+</span>
                </div>

                <div class="group1 flex-col" v-else>
                  <img class="qr" :src="getOssUrl('dl_mobile.png')" />
                  <span class="txt2">扫码下载</span>
                </div>
              </div>
              <ElTooltip content="点击下载Mac客户端" placement="top">
                <div class="section5 flex-col">
                  <div class="box2 flex-col">
                    <div class="box3 flex-col" @click="download('mac')">
                      <div class="mac_note">已支持M1/M2芯片</div>
                      <img class="pic2" :src="getOssUrl('dl_5.png')" />
                    </div>
                    <span class="info2">MacOS客户端</span>
                    <span class="txt3"> MacOS&nbsp;11+ </span>
                  </div>
                </div>
              </ElTooltip>
              <ElTooltip content="点击下载Windows客户端" placement="top">
                <div class="section6 flex-col">
                  <div class="mod3 flex-col">
                    <div class="layer2 flex-col pointer" @click="download('win')">
                      <div class="box4 flex-col">
                        <div class="mod5 flex-col"></div>
                      </div>
                    </div>
                    <span class="info3">Windows客户端</span>
                    <span class="word2">Windows&nbsp;10+</span>
                  </div>
                </div>
              </ElTooltip>
            </div>
          </div>
        </div>
        <div class="word4">
          江苏绚星智慧科技有限公司 2011-{{ year }}. All rights reserved.
        </div>
      </div>
      <div class="box5 flex-col">
        <img class="img1" :src="getOssUrl('dl_6_4.png')" />
      </div>
    </div>

    <!-- 下载进度遮罩层 -->
    <div v-if="showDownloadProgress" class="download-overlay">
      <div class="download-modal">
        <div class="download-content">
          <div class="download-icon">
            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
              <path d="M24 4L24 32M24 32L12 20M24 32L36 20" stroke="#1890ff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M8 40H40" stroke="#1890ff" stroke-width="3" stroke-linecap="round"/>
            </svg>
          </div>
          <h3 class="download-title">正在下载客户端</h3>
          <p class="download-tip">请勿关闭浏览器窗口，下载完成后将自动保存</p>
          
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: downloadProgress + '%' }"></div>
            </div>
            <span class="progress-text">{{ downloadProgress }}%</span>
          </div>
          
          <div class="download-info">
            <span class="download-filename">{{ downloadFilename }}</span>
            <span class="download-size">{{ downloadSize }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getOssUrl, jsOpenNewWindow, isPC } from "@/js/utils.js";
import { ElTooltip } from "element-plus";

export default {
  components: {
    ElTooltip,
  },
  data() {
    return {
      show_android_qr: false,
      show_ios_qr: false,
      year: new Date().getFullYear(),
      showDownloadProgress: false,
      downloadProgress: 0,
      downloadFilename: '',
      downloadSize: '',
    };
  },
  mounted() {
    if (!isPC()) {
      g.appStore.toMobileDownload()
    }
  },
  methods: {
    getOssUrl,
    toPage() {
      window.open(g.config.publicPath, "_self");
    },
    download(type) {
      const baseurl = g.config.downloadPath;
      let ymlurl = "";
      if (type == "win") {
        ymlurl = baseurl + "windows/latest.yml";
      } else {
        ymlurl = baseurl + "mac/latest-mac.yml";
      }
      fetch(ymlurl)
        .then((res) => {
          if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
          }
          return res.text();
        })
        .then((text) => {
          // Parse the YAML content
          const lines = text.split("\n");
          let version = "";
          let filename = "";

          for (const line of lines) {
            if (line.startsWith("version:")) {
              version = line.split(":")[1].trim();
            } else if (
              line.includes("url:") &&
              (line.includes(".exe") || line.includes(".dmg"))
            ) {
              filename = line.split(":")[1].trim();
            }
          }

          if (version && filename) {
            // Construct the full download URL
            const downloadUrl = `${baseurl}${type === "win" ? "windows" : "mac"
              }/${filename}`;
            this.downloadWithReferer(downloadUrl);
          } else {
            console.error("Failed to parse version or filename from yml");       
          }
        })
        .catch((err) => {
          console.error("Failed to fetch or parse yml file:", err);          
        });
    },

    downloadWithReferer(url) {
      // 获取当前页面的 origin 作为 referer
      const referer = window.location.origin;
      
      // 显示下载进度界面
      this.showDownloadProgress = true;
      this.downloadProgress = 0;
      this.downloadFilename = url.split('/').pop();
      this.downloadSize = '';
      
      // 使用 XMLHttpRequest 来跟踪下载进度
      const xhr = new XMLHttpRequest();
      
      xhr.open('GET', url, true);
      xhr.setRequestHeader('Referer', referer);
      // 设置 responseType 为 blob 以正确处理二进制文件
      xhr.responseType = 'blob';
      
      // 监听下载进度
      xhr.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentComplete = Math.round((event.loaded / event.total) * 100);
          this.downloadProgress = percentComplete;
          
          // 格式化文件大小
          const loaded = this.formatFileSize(event.loaded);
          const total = this.formatFileSize(event.total);
          this.downloadSize = `${loaded} / ${total}`;
        }
      });
      
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          // 直接使用 xhr.response (已经是 Blob)
          const blob = xhr.response;
          const downloadUrl = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = downloadUrl;
          a.download = this.downloadFilename;
          
          document.body.appendChild(a);
          a.click();
          
          // 清理
          window.URL.revokeObjectURL(downloadUrl);
          document.body.removeChild(a);
          
          // 隐藏进度界面
          setTimeout(() => {
            this.showDownloadProgress = false;
            this.downloadProgress = 0;
            this.downloadFilename = '';
            this.downloadSize = '';
          }, 1000);
        } else {
          throw new Error(`HTTP error! status: ${xhr.status}`);
        }
      });
      
      xhr.addEventListener('error', () => {
        console.error('Download failed');
        this.showDownloadProgress = false;
        // 如果下载失败，回退到原来的方法
        jsOpenNewWindow(url);
      });
      
      xhr.send();
    },
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    mouseOverAndroid() {
      this.show_android_qr = true;
    },
    mouseLeaveAndroid() {
      this.show_android_qr = false;
    },
    mouseOverIos() {
      this.show_ios_qr = true;
    },
    mouseLeaveIos() {
      this.show_ios_qr = false;
    },
  },
};
</script>

<style scoped lang="scss">
@import url("./index.scss");

// 下载进度遮罩层样式
.download-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.download-modal {
  background: white;
  border-radius: 12px;
  padding: 32px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.download-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.download-icon {
  margin-bottom: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.download-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.download-tip {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.progress-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 16px 0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.download-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.download-filename {
  font-size: 12px;
  color: #999;
  word-break: break-all;
}

.download-size {
  font-size: 12px;
  color: #999;
}
</style>
