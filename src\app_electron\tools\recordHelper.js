import { recordIpcCall } from './performanceMonitor';

class AVLocalRecorder {
    constructor() {
        this.mediaRecorder = null;
        this.recordingInterval = null;
        this.stream = null;
        this.audioBuffer = []; // 添加音频缓冲区
        this.bufferSize = 0; // 缓冲区大小
        this.maxBufferSize = 2 * 1024 * 1024; // 降低到2MB缓冲区限制
        this.minBufferSize = 512 * 1024; // 最小512KB就触发写入
        this.flushInterval = null; // 定期刷新缓冲区
        this.lastFlushTime = 0; // 上次刷新时间
    }

    async startRecording(stream, options) {
        try {
            g.ipcRenderer.invoke('create_new_record_file', options);
            recordIpcCall(); // 记录IPC调用
            this.stream = stream;

            // 优化音频录制参数，确保时间戳正确
            const mediaRecorderOptions = {
                mimeType: 'audio/webm;codecs=opus', // 使用更高效的编码格式
                audioBitsPerSecond: 128000 // 降低比特率以减少CPU使用
            };

            // 检查浏览器是否支持该格式
            if (!MediaRecorder.isTypeSupported(mediaRecorderOptions.mimeType)) {
                // 如果不支持，使用默认格式
                this.mediaRecorder = new MediaRecorder(stream);
            } else {
                this.mediaRecorder = new MediaRecorder(stream, mediaRecorderOptions);
            }

            this.mediaRecorder.ondataavailable = async (event) => {
                if (event.data.size > 0) {
                    const arrayBuffer = await event.data.arrayBuffer();
                    this.audioBuffer.push(arrayBuffer);
                    this.bufferSize += arrayBuffer.byteLength;

                    // 当缓冲区超过限制时，批量发送数据
                    if (this.bufferSize >= this.maxBufferSize) {
                        this.flushAudioBuffer();
                    }
                }
            };

            this.mediaRecorder.onstop = async () => {
                // 停止时刷新剩余缓冲区
                this.flushAudioBuffer();
            };

            // 增加数据收集间隔到2秒，减少CPU使用
            this.mediaRecorder.start(2000);

            // 设置定期刷新缓冲区，每3秒刷新一次（减少间隔）
            this.flushInterval = setInterval(() => {
                const now = Date.now();
                // 如果距离上次刷新超过3秒且有数据，则刷新
                if (now - this.lastFlushTime >= 3000 && this.audioBuffer.length > 0) {
                    this.flushAudioBuffer();
                }
            }, 1000); // 每秒检查一次，但3秒才执行
        } catch (error) {
            console.error('Error starting recording:', error);
            ElMessage.error('启动录音失败');
        }
    }

    flushAudioBuffer() {
        if (this.audioBuffer.length > 0) {
            // 合并所有缓冲区数据
            const totalSize = this.audioBuffer.reduce((size, buffer) => size + buffer.byteLength, 0);
            const combinedBuffer = new ArrayBuffer(totalSize);
            const uint8Array = new Uint8Array(combinedBuffer);

            let offset = 0;
            for (const buffer of this.audioBuffer) {
                uint8Array.set(new Uint8Array(buffer), offset);
                offset += buffer.byteLength;
            }

            // 批量发送数据
            g.ipcRenderer.invoke('send-audio-data', combinedBuffer);
            recordIpcCall(); // 记录IPC调用

            // 清空缓冲区
            this.audioBuffer = [];
            this.bufferSize = 0;
            this.lastFlushTime = Date.now();
        }
    }

    stopRecording() {
        if (this.recordingInterval) {
            clearInterval(this.recordingInterval);
            this.recordingInterval = null;
        }

        if (this.flushInterval) {
            clearInterval(this.flushInterval);
            this.flushInterval = null;
        }

        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.stop();
        }

        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
        }
        this.mediaRecorder = null;
        this.stream = null;
        // 注意：end_local_record 现在在 localRecord.js 的 stopLocalRecorder 中调用
        recordIpcCall(); // 记录IPC调用
    }

    pauseRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.pause();
            // 暂停时立即刷新缓冲区，避免数据丢失
            this.flushAudioBuffer();

            if (this.recordingInterval) {
                clearInterval(this.recordingInterval);
                this.recordingInterval = null;
            }
            if (this.flushInterval) {
                clearInterval(this.flushInterval);
                this.flushInterval = null;
            }
        }
    }

    resumeRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state === 'paused') {
            this.mediaRecorder.resume();
            // 重新启动定期刷新
            this.flushInterval = setInterval(() => {
                const now = Date.now();
                // 如果距离上次刷新超过3秒且有数据，则刷新
                if (now - this.lastFlushTime >= 3000 && this.audioBuffer.length > 0) {
                    this.flushAudioBuffer();
                }
            }, 1000);
        }
    }

    getState() {
        return this.mediaRecorder ? this.mediaRecorder.state : 'inactive';
    }
}

export default AVLocalRecorder;
