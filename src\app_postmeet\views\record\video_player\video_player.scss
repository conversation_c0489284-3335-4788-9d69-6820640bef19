.vpw_has_tag {
  margin: 7px 0 56px 4px;
}

.vpw_has_notag {
  // margin: 7px 0 16px 4px;
}

.vpw_hasasr {
  height: 364px;

  video {
    height: 364px;
  }
}


.video_player_wrap {
  visibility: hidden;
  height: 0px;
  padding: 0px 0 0 10px;
  position: relative;
  width: 97%;

  .cover_txt {
    position: absolute;
    top: 10px;
    left: 10px;
    padding-top: 41px;
    padding-left: 41px;
    pointer-events: none;

    .subject {
      font-size: 31px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 45px;
    }

    .note {
      font-size: 17px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 31px;
    }

    .customerName {
      margin-bottom: 16px;
    }

  }

  .audio_box {
    height: 54px;
  }

  .has_analyse {
    padding-right: 379px;
  }

  .video_box {
    video {
      background-color: #436bff;
      position: relative;
    }

    .video_started {
      background-color: #000;
    }

    video::cue {
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      padding: 0.5em;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      position: absolute;
      left: 0;
      top: 0;
    }

    .fsize_1::cue {
      font-size: 0.6em !important;
    }

    .fsize_2::cue {
      font-size: 0.8em !important;
    }

    .fsize_3::cue {
      font-size: 1.0em !important;
    }

    .fsize_4::cue {
      font-size: 1.2em !important;
    }

    .fsize_5::cue {
      font-size: 1.4em !important;
    }

    .markers_share {
      bottom: 30px;

      .mark {
        background-color: #f76812;
      }
    }

    .markers_split {
      bottom: 40.5px;

      .mark {
        background-color: greenyellow;
      }
    }
  }

  .video_footer {
    // margin-top: -20px;

    .video_toolbar {
      display: flex;
      margin-bottom: 12px;

      .tag_name {
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 423px;
      }

      .flex_grow {
        flex-grow: 1;
      }

      .right_btn {
        color: #436bff;
        cursor: pointer;
        padding-bottom: 6px;
      }

      .right_zoom_btn {
        width: 62px;
        height: 24px;
        display: flex;

        .btn {
          color: #595959;
          height: 20px;
          width: 20px;
          line-height: 20px;
          font-size: 20px;
          border: 1px solid #eee;
          border-radius: 5px;
          text-align: center;
          cursor: pointer;
          margin-left: 5px;
          user-select: none;
        }

        .disable {
          color: #bfbfbf;
          background-color: #F5F5F5;
          cursor: default;
        }
      }
    }

    .markers_tag {
      border-radius: 2px;
      height: 4px;
      background-color: #eee;

      .mark {
        background-color: #436bff;
      }
    }
  }

}

.vpw_noasr {
  .video_box {
    video {
      background-color: #fff;
    }

    .video_started {
      background-color: #fff !important;
    }
  }
}