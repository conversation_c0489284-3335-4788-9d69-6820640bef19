<template>
    <div class="customer-voice-faq-h5">
        <div class="title-text">
            客户常见问题（FAQ）
            <el-tooltip content="展示用户反馈中最常出现的问题及其所属维度" placement="top" popper-class="custom-tooltip">
                <img :src="getOssUrl('question.png', 3)" alt="faq" class="faq_icon" />
            </el-tooltip>
        </div>
        <div class="faq-card-container" v-loading="loading">
            <div class="faq-card">
                <div class="faq-stats" :class="{ 'isDown': isDown }">
                    <div class="stat-item" :class="{ 'active': !selectedDimension }" @click="toggleDimension('')">
                        <div class="stat-value">100%</div>
                        <div class="stat-label">全部</div>
                    </div>
                    <div class="stat-item" v-for="(value, key) in stats" :key="key"
                        :class="{ 'active': selectedDimension === value.dimensionId }"
                        @click="toggleDimension(value.dimensionId)">
                        <div class="stat-value">{{ value.qaRatio }}%</div>
                        <div class="stat-label">{{ value.dimensionName }}</div>
                    </div>
                    <div class="stat-item-action">
                        <van-icon name="arrow-up" v-if="isDown" @click="isDown = false" />
                        <van-icon name="arrow-down" v-else @click="isDown = true" />
                    </div>
                </div>
            </div>
            <div class="faq-list" v-loading="loadingLeft">
                <div class="list-items">
                    <div v-for="(item, index) in faqList" :key="item.dimensionId" class="list-item"
                        @click="onOpenDetails(item)">
                        <div class="item-index">{{ index + 1 }}</div>
                        <div class="item-content">
                            <div class="item-title single-line-ellipsis">
                                {{ item.question }}
                            </div>
                            <div :class="['item-tag', `tag_${getTagClass(item.dimensionName)}`]">{{
                                item.dimensionName }}</div>
                        </div>
                        <div class="item-count">{{ item.qaCount }}条</div>
                    </div>
                </div>
            </div>
        </div>
        <FaqDetails ref="refFatDetail" v-if="isShowFaqDetails" @close="isShowFaqDetails = false"></FaqDetails>
    </div>
</template>
<script setup>
import FaqDetails from './faqDetails.vue'
import { getOssUrl } from "@/js/utils.js";
const store = g.clientBiStore
const refFatDetail = ref()
const isDown = ref(false)
const isShowFaqDetails = ref(false)
const loadingLeft = ref(false)
// 统计数据
const stats = computed(() => store.cvFaqDistList)

// 选中的维度ID
const selectedDimension = ref(null)

// FAQ列表数据
// 修改 FAQ 列表数据计算属性
const faqList = computed(() => {
    const list = store.cvFaqQuestionsCount
    if (selectedDimension.value) {
        return list.filter(item => item.parentDimensionId === selectedDimension.value)
    }
    return list
})

// 切换维度选中状态
const toggleDimension = (dimensionId) => {
    if (selectedDimension.value === dimensionId) {
        selectedDimension.value = null
    } else {
        selectedDimension.value = dimensionId
    }
}

// 问答列表数据
// const qaList = computed(() => store.cvFaqQAList)
const loading = ref(false);
const finished = ref(false);

const isHide = ref(true)
const isShowTooltip = (val, e) => {
    isHide.value = g.appStore.isShowTooltip(val, e, 14, 2);
}

const onOpenDetails = (item) => {
    // refFatDetail.value.show(item)
    isShowFaqDetails.value = true
    nextTick(() => {
        refFatDetail.value.show(item)
    })
}
const init = async () => {
    loading.value = true
    loadingLeft.value = true;
    await Promise.all([
        store.getCvFaqDistList(),
        store.getCvFaqQuestionsCount(),
        // store.getCvFaqQAList()
    ])
    loadingLeft.value = false;
    loading.value = false
    finished.value = true;
}
const getTagClass = (name) => {
    const index = stats.value.findIndex(i => i.dimensionName == name)
    if (index > -1) return index + 1
    return 1
}

watch(() => g.clientBiStore.periodType, () => {
    init()
}, { immediate: true })

onMounted(async () => {
    init()
})

</script>
<style lang="scss" scoped>
.customer-voice-faq-h5 {
    padding-bottom: 0;

    .title-text {
        display: flex;
        align-items: center;

    }

    .faq_icon {
        width: 16px;
        height: 16px;

    }

    .faq-card-container {
        background: #FFFFFF;
        border-radius: 12px;
        margin-top: 16px;
        padding: 16px;

        .faq-card {
            .isDown {
                flex-wrap: wrap;
            }

            .faq-stats {
                display: flex;
                margin-bottom: 16px;
                gap: 12px;
                position: relative;
                overflow: hidden;


                .stat-item-action {
                    background: #fff;
                    position: absolute;
                    height: 72px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 20px;
                    background: linear-gradient(270deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
                    top: 0;
                    right: 0;
                    color: #436BFF;
                    z-index: 1;
                }



                .stat-item {
                    background: #F5F5F5;
                    border-radius: 8px;
                    padding: 12px 12px;
                    word-break: keep-all;
                    border: 1px solid #F5F5F5;
                    min-width: calc(33% - 15px);
                    box-sizing: border-box;


                    // 添加选中状态样式
                    &.active {
                        // background: #E6F7FF;
                        border: 1px solid #436BFF;

                        .stat-value,
                        .stat-label {
                            color: #436BFF;

                        }
                    }

                    .stat-value {
                        font-size: 20px;
                        font-weight: 600;
                        color: #262626;
                    }

                    .stat-label {
                        font-size: 12px;
                        color: #8C8C8C;
                        margin-top: 4px;
                    }
                }
            }
        }

        .faq-list {
            width: 100%;
            // border-radius: 8px;
            background: #fff;
            box-sizing: border-box;

            .list-items {

                border-radius: 4px;
                // padding: 12px 0;
                box-sizing: border-box;
                width: 100%;

                .single-line-ellipsis {

                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .list-item {
                    box-sizing: border-box;
                    padding: 16px 12px;
                    display: flex;
                    align-items: center;
                    // justify-content: space-between;
                    cursor: pointer;
                    height: 50px;

                    .item-content {
                        // flex: 1;
                        display: flex;
                        flex-direction: row;
                        justify-content: flex-start;
                        align-items: center;
                        width: calc(100% - 78px);

                        .item-title {
                            // width: calc(100% - 84px);
                        }

                        .item-tag {
                            padding: 2px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            margin-left: 12px;
                            box-sizing: border-box;


                            &.tag_1 {
                                background: rgba(51, 196, 255, 0.10);
                                color: #33C4FF;
                            }

                            &.tag_2 {
                                background: rgba(4, 204, 164, 0.10);
                                color: #04CCA4;
                            }

                            &.tag_3 {
                                background: rgba(246, 189, 22, 0.10);
                                color: #F6BD16;
                            }

                            &.tag_4 {
                                background: rgba(255, 107, 59, 0.10);
                                color: #FF6B3B;
                            }

                            &.tag_5 {
                                background: rgba(91, 143, 249, 0.10);
                                color: #5B8FF9;
                            }

                            &.tag_6 {
                                background: rgba(255, 117, 173, 0.10);
                                color: #FF75AD;
                            }

                            &.tag_7 {
                                background: rgba(141, 122, 242, 0.10);
                                color: #8D7AF2;
                            }

                            &.tag_8 {
                                background: rgba(40, 156, 203, 0.10);
                                color: #289ccb;
                            }
                        }

                        .item-title {
                            font-size: 14px;
                            color: #262626;
                        }
                    }

                    &:hover {
                        background: #F5F5F5;
                    }

                    &.active {
                        background: #F0F6FF;

                        .item-title {

                            // flex: 1;
                            color: #436BFF;
                        }
                    }

                    .item-index {
                        width: 24px;
                        color: #436BFF;
                        font-size: 14px;
                        font-weight: 600;
                        line-height: 24px;
                    }


                    .item-count {
                        color: #8C8C8C;
                        font-size: 14px;
                        min-width: 50px;
                        text-align: right;
                    }
                }
            }
        }

        .faq-list-item-c {
            // display: inline-block;
            // width: 64px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }
    }
}
</style>