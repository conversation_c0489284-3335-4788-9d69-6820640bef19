// 性能监控工具
class PerformanceMonitor {
    constructor() {
        this.startTime = null;
        this.metrics = {
            cpuUsage: [],
            memoryUsage: [],
            audioBufferSize: [],
            writeOperations: 0,
            ipcCalls: 0
        };
        this.isMonitoring = false;
        this.monitorInterval = null;
    }

    startMonitoring() {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        this.startTime = Date.now();
        this.metrics = {
            cpuUsage: [],
            memoryUsage: [],
            audioBufferSize: [],
            writeOperations: 0,
            ipcCalls: 0
        };

        // 每2秒收集一次性能数据
        this.monitorInterval = setInterval(() => {
            this.collectMetrics();
        }, 2000);

        g.elog.log('Performance monitoring started');
    }

    stopMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }

        const duration = Date.now() - this.startTime;
        const report = this.generateReport(duration);
        g.elog.log('Performance monitoring stopped', report);

        return report;
    }

    collectMetrics() {
        // 收集内存使用情况
        if (performance.memory) {
            this.metrics.memoryUsage.push({
                timestamp: Date.now(),
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            });
        }

        // 记录音频缓冲区大小
        if (window.audioContext) {
            this.metrics.audioBufferSize.push({
                timestamp: Date.now(),
                state: window.audioContext.state,
                sampleRate: window.audioContext.sampleRate
            });
        }
    }

    recordWriteOperation() {
        this.metrics.writeOperations++;
    }

    recordIpcCall() {
        this.metrics.ipcCalls++;
    }

    generateReport(duration) {
        const avgMemoryUsage = this.metrics.memoryUsage.length > 0
            ? this.metrics.memoryUsage.reduce((sum, m) => sum + m.used, 0) / this.metrics.memoryUsage.length
            : 0;

        const maxMemoryUsage = this.metrics.memoryUsage.length > 0
            ? Math.max(...this.metrics.memoryUsage.map(m => m.used))
            : 0;

        return {
            duration: duration / 1000, // 秒
            avgMemoryUsage: Math.round(avgMemoryUsage / 1024 / 1024), // MB
            maxMemoryUsage: Math.round(maxMemoryUsage / 1024 / 1024), // MB
            writeOperations: this.metrics.writeOperations,
            ipcCalls: this.metrics.ipcCalls,
            writeOperationsPerSecond: (this.metrics.writeOperations / (duration / 1000)).toFixed(2),
            ipcCallsPerSecond: (this.metrics.ipcCalls / (duration / 1000)).toFixed(2)
        };
    }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor();

// 导出性能监控函数
export const startPerformanceMonitoring = () => {
    performanceMonitor.startMonitoring();
};

export const stopPerformanceMonitoring = () => {
    return performanceMonitor.stopMonitoring();
};

export const recordWriteOperation = () => {
    performanceMonitor.recordWriteOperation();
};

export const recordIpcCall = () => {
    performanceMonitor.recordIpcCall();
};

export default performanceMonitor;
