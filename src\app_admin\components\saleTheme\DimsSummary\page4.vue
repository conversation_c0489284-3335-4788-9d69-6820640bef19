<template>
    <div class="example_pic">示例图</div>
    <img :src="getImg" class="page_img" alt="img" />
</template>
<script setup>
import { getOssUrl } from "@/js/utils";
const props = defineProps(['img', 'systemId']);

const getImg = computed(() => {
    if (props.systemId == 4) {
        return getOssUrl('salesmethod_normal2.png', 2);
    } else if (props.systemId == 8) {
        return getOssUrl('salesmethod_' + props.img.toLowerCase() + '.png');
    }
});

defineExpose({ getImg });
</script>

<style lang="scss">
.example_pic {
    width: 52px;
    height: 20px;
    background: #f5f5f5;
    border-radius: 2px;
    font-size: 12px;
    color: #595959;
    line-height: 20px;
    text-align: center;
    margin-left: 7px;
    padding: 0 2px;
}
</style>