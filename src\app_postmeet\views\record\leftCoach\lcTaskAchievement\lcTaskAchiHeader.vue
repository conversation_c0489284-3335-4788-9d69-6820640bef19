<template>
  <div class="la_task_achi_header flex-column">
    <div class="la_box la_main flex-column">
      <div class="la_header flex-row">
        <div class="la_left flex-row">
          <div class="la_title">任务达成率</div>
          <div class="la_progress">
            完成任务数：{{ completedCount }} / 总任务数：{{ totalCount }}
          </div>
        </div>
        <div class="la_detail" @click="toggleDetail">
          {{ isExpanded ? "收起" : "展开详情" }}
        </div>
      </div>
      <div :class="`la_score flex-row ${score > targetSettings ? 's2' : score > 60 ? 's1' : 's0'}`">
        <div class="la_score_num">{{ score }}</div>
        <div class="la_score_unit">%</div>
      </div>
    </div>

    <div v-if="isExpanded" class="la_detail_content flex-column">
      <div class="la_evaluation">
        <div class="la_eval_title">
          <i class="la_icon">
            <taskAch1 />
          </i>整体评价
        </div>
        <div class="la_eval_content">
          {{ headInfo.overallEvaluation }}
        </div>
      </div>
      <div class="la_line"></div>
      <div class="la_suggestion">
        <div class="la_sugg_title">
          <i class="la_icon">
            <taskAch2 />
          </i>改进建议
        </div>
        <div class="la_sugg_content">
          <ol>
            <li v-for="(item, index) in headInfo.improvementSuggestions" :key="index">
              {{ item }}
            </li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import taskAch1 from "@/app_postmeet/icons/taskAch1.vue";
import taskAch2 from "@/app_postmeet/icons/taskAch2.vue";
import { ref } from "vue";
import { calculateTaskScore } from "@/app_postmeet/tools/tools.js";

const headInfo = ref({});
const taskList = ref([]);
const targetSettings = ref(80)
const totalCount = computed(() =>
  taskList.value.reduce((sum, item) => sum + item.totalCount, 0)
);
const completedCount = computed(() =>
  taskList.value.reduce((sum, item) => sum + item.completedCount, 0)
);

const score = computed(() => {
  return calculateTaskScore(completedCount.value, totalCount.value);
});

const isExpanded = ref(false);

const toggleDetail = () => {
  isExpanded.value = !isExpanded.value;
};
const init = () => {
  const ar = g.postmeetStore.data.saleReport.salesCounsellingReports.find(
    (x) => x.systemId == 206
  );
  targetSettings.value = ar.targetSettings;
  headInfo.value = ar.report?.response || {};
  const tasks = ar.report?.tasks || [];
  taskList.value = tasks.map((item) => {
    return {
      ...item,
      totalCount: item.content?.evaluationResults?.subTasks.length || 0,
      completedCount: item.content?.evaluationResults?.subTasks.filter(
        (x) => x.status === "complete"
      ).length || 0,
    };
  });
};

onMounted(() => {
  init();
});

defineExpose({
  toggleDetail,
  headInfo,
});
</script>

<style lang="scss">
.la_task_achi_header {
  position: relative;

  .la_box {
    margin: 12px 0 0 0;
    background: #f1f3fb;
    border-radius: 8px 8px 0px 0px;
  }

  .la_score {
    margin-right: 16px;

    .la_score_num {
      font-weight: 500;
      font-size: 20px;
      line-height: 30px;
    }

    .la_score_unit {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      margin-top: 6px;
    }
  }

  .la_detail_content {
    padding: 20px 16px;
    background: #f9fafc;
    border-radius: 0px 0px 4px 4px;

    .la_evaluation,
    .la_suggestion {

      .la_eval_title,
      .la_sugg_title {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;

        .la_icon {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }

      .la_eval_content,
      .la_sugg_content {
        font-size: 14px;
        line-height: 22px;
        color: #262626;

        li {
          list-style: decimal;
          margin-left: 18px;
        }
      }
    }

    .la_line {
      width: 100%;
      height: 1px;
      background: #e4e7ed;
      margin: 16px 0;
    }

    .la_suggestion {
      margin-top: 16px;
    }
  }
}
</style>
