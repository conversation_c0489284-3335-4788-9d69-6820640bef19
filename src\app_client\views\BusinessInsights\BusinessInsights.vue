<template>
    <div class="business_insights_container">
        <div class="tab_content" ref="exportContent">
            <filterForm class="tab-filter-form">
            </filterForm>
            <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane label="客户之声" name="CUSTOMER_VOICE" lazy>
                    <customerVoice />
                </el-tab-pane>
                <el-tab-pane label="需求洞察" name="requirementInsight" lazy>
                    <requirementInsight />
                </el-tab-pane>
                <el-tab-pane label="竞品雷达" name="COMPETITOR_RADAR" lazy>
                    <competitiveRadar />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<script setup lang="js">
import competitiveRadar from './competitiveRadar/competitiveRadar.vue'
import customerVoice from './customerVoice/customerVoice.vue'
import requirementInsight from './requirementInsight/requirementInsight.vue'
import filterForm from './components/filterForm.vue'
import exportElementToPDF from '@/js/htmlToPdf.js'
import { onActivated } from 'vue'
const activeName = ref('CUSTOMER_VOICE')
const exportContent = ref(null)
const exporting = ref(false)

watch(() => activeName.value, (newVal) => {
    g.clientBiStore.setSelectTabType(newVal)
}, { immediate: true })

const handleExport = async () => {
    if (!exportContent.value) {
        ElMessage.warning('未找到可导出的内容')
        return
    }

    try {
        exporting.value = true
        await exportElementToPDF(exportContent.value, '竞品分析报告')
        ElMessage.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
        ElMessage.error('导出失败，请重试')
    } finally {
        exporting.value = false
    }
}

onActivated(() => {
    g.clientBiStore.setSelectTabType('')
    g.clientBiStore.setSelectTabType(activeName.value)
})
</script>

<style lang="scss">
.business_insights_container {
    .el-empty {
        --el-empty-image-width: 320px !important;
    }

    padding: 20px 24px;
    background: #F3F4F7;
    box-sizing: border-box;

    .business_insights_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 18px;
    }

    h2 {
        font-weight: 700;
        font-size: 20px;
        color: #262626;
        line-height: 26px;
        text-align: left;
        font-style: normal;

    }

    .el-tabs__active-bar {
        background-color: #436BFF !important;
    }

    .is-active {
        color: #436BFF !important;
    }

    .tab_content {
        background: #FFFFFF;
        border-radius: 8px;
        padding: 12px 24px 0px 24px;
        box-sizing: border-box;
        position: relative;
    }

    .el-tabs__item {
        font-weight: 400;
        font-size: 16px;
        color: #595959;
        line-height: 24px;
    }

    .tab-filter-form {
        position: absolute;
        right: 24px;
        top: 10px;
    }

}

.category_list_wrap {
    background: #FFFFFF;
    border-radius: 4px;
    height: 600px;
    width: 240px;
    padding: 12px 0;
    box-sizing: border-box;
    overflow: scroll;

    .category_menu {
        border-right: none;


        .el-sub-menu__title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            height: 50px;
            box-sizing: border-box;
            font-weight: 700;

            &:hover {
                background: #F5F5F5;
            }
        }

        .el-menu-item-else-title {
            font-weight: 700;
        }

        .el-menu-item-group__title {
            padding: 0 24px;
            font-size: 14px;
            color: #8C8C8C;
        }

        .el-menu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            margin: 0;

            &.is-active {
                background-color: #F0F7FF !important;
            }

            &:hover {
                background: #F5F5F5;
            }

            .star2 {
                width: 16px;
                height: 16px;
                margin-right: 8px;
                vertical-align: middle;
            }

            .item_count {
                font-size: 12px;
                color: #8C8C8C;
                border-radius: 10px;
                padding: 2px 8px;
            }
        }

        .menu-title {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .star2 {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            vertical-align: middle;
        }

        .item_count {
            font-size: 12px;
            color: #8C8C8C;
            border-radius: 10px;
            padding: 2px 8px;
        }

        // 子菜单样式
        .el-sub-menu .el-menu-item {
            padding-left: 48px;
            // margin: 2px 0;
        }

        // 折叠图标样式
        .el-sub-menu__icon-arrow {
            color: #8C8C8C;
            font-size: 12px;
        }

        // 激活状态的子菜单
        .el-sub-menu.is-active .el-sub-menu__title {
            height: 50px;
        }
    }
}

.filter_condition_wrap {
    padding: 16px 0;
    gap: 16px;

    .fch_filter {
        border-radius: 4px;

        .filter_item {
            display: flex;
            align-items: center;
            margin-right: 24px;

            .filter_select {
                width: 220px;
            }

            .filter_customer {
                width: 380px;
            }

            .filter_label {
                font-size: 14px;
                color: #262626;
                margin-right: 8px;
            }
        }

        .filter_btn {
            margin-left: auto;

            // margin-left: 8px;
            background: rgba(255, 255, 255, 0.01);
            border-radius: 4px;
            border: 1px solid #436BFF;
            font-size: 14px;
            color: #436BFF;
        }
    }
}
</style>