@use "@/app_postmeet/config/config.scss";

.export_ddl_body {
  padding: 5px 10px;

  div {
    margin: 7px 0;

    .el-button--small {
      width: 122px;
    }
  }
}

.subtitles-video {
  height: calc(100vh - 590px);
}

.subtitles-audio {
  height: calc(100vh - 218px);
}

.subtitles-wrap {
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-direction: column;

  .title {
    margin: 24px 0 10px 0;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .sub_title {
      color: #bbb;
    }

    .fold_icon {
      margin-left: 10px;
    }

    .yxtf-button--primary {
      width: 82px;
    }

    span {
      margin-right: 10px;
    }

    .word_count {
      font-size: 12px;
      color: #999;
      font-weight: 400;
      margin-top: 6px;
    }

    .flex-grow {
      flex-grow: 1;
    }

    .btnChat {
      margin-left: 10px;
    }

  }

  .subtitles {
    padding: 0 15px;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .speak-time {
      padding-right: 24px;
      transition: padding-right 0.3s;

      &.has-analyse {
        padding-right: 379px;
      }
    }

    .title {
      display: flex;
      align-items: center;
      padding: 12px 24px 12px 0;
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      transition: padding-right 0.3s;

      &.has-analyse {
        padding-right: 379px;
      }

      .word_count {
        margin-left: 8px;
        font-size: 12px;
        color: #8C8C8C;
      }
    }

    .fold_wrap {
      display: flex;

      .tags_left {
        flex-grow: 1;
        width: calc(100% - 70px);
      }

      .tags_right {
        width: 70px;
      }
    }

    .fold_wrap:hover {
      color: #436bff;
    }

    .name_wrap {
      border-top: 1px solid #eee;
      padding-top: 5px;
    }
  }


  .tags {
    display: flex;
    align-items: center;
    padding: 3px 0;
    flex-wrap: wrap;

    li.tagname {
      color: unset;
      background-color: unset;
      border: unset;
      cursor: unset;
      font-weight: bold;
      font-size: 15px;
      padding: 0;
      margin-left: -8px;
      margin-right: 10px;
    }

    li {
      margin: 0 20px 10px 0;
      padding: 1px 9px;
      background-color: #F5F5F5;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #262626;
      border-radius: 4px;
      cursor: pointer;
      border: 1px solid #f2f2f2;

      &.active {
        color: #436bff;
        border-color: #436bff;
      }
    }
  }

  .empty_box {
    display: revert;
    width: calc(50vw - 77px);
  }

  .subtitles-list {
    display: revert;

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      background-color: #f5f5f5;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: #e0e0e0;
    }

    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background-color: #fff;
    }

    .ai-generated-tip {
      margin-bottom: 16px;
    }

    li {
      margin: 8px 0;
      display: flex;
      align-items: stretch;

      .subtitles-list-left {
        display: flex;
        flex-grow: 1;
        flex-direction: column;
        width: auto;
      }

      .name_time {
        font-size: 13px;
        color: #999;
        margin-bottom: 0;
        display: flex;
        justify-content: space-between;

        .name_box {
          margin-right: 12px;

          display: flex;
          padding: 0 3px;

          .t_name {
            margin-right: 6px;
          }
        }

        .edit_name {
          cursor: pointer;

          .editicon {
            margin-top: 3px;
            width: 20px;
            font-size: 16px;

            svg {
              display: none;
            }
          }
        }

        .edit_name:hover {
          border-radius: 4px;

          .editicon {
            svg {
              display: block;
            }
          }
        }

        .sb_name_right {
          margin-right: 12px;

          .el-icon {
            cursor: pointer;
            display: none;
          }
        }
      }

      .tl_label {
        position: relative;
        top: -1px;
        left: 10px;
        color: #fff !important;
        border-radius: 5px;
        padding-left: 4px;
        font-size: 12px;
        height: 19px;
        line-height: 19px;
      }

      .answer_label {
        background: #605CE5;
        width: 16px;
      }

      .question_label {
        background: #FE9D2B;
        width: 16px;
      }

      .qa_label {
        background: #605CE5;
        width: 28px;
      }

      .msg_box {
        background: #EFF2FC;
        border-radius: 0px 2px 1px 2px;
        padding: 2px;
        margin-top: 5px;

        .txt {
          cursor: pointer;
          padding: 2px 8px;
        }

        .split_line {
          width: 99%;
          height: 1px;
          background: #E9E9E9;
        }

        .light_txt {
          color: #8c8c8c;
        }

        .normal_txt {
          color: #333;
        }

        .qa_bk {
          background-color: #E7E7FB;
          color: #434267 !important;
        }

        .tp_box {
          .textpolish_icon {
            margin: 3px 10px;
          }

          .tp_txt {
            height: 22px;
            font-size: 12px;
            color: #436BFF;
            line-height: 22px;
          }
        }
      }

      .subtitle_diy {
        input {
          width: 96%;
        }
      }
    }

    li:hover {
      .sb_name_right {
        .el-icon {
          display: block;
        }
      }
    }

    .highli {


      .msg_box .normal_txt {
        color: #fff;
      }

      .msg_box {
        background-color: #436BFF;

        .tp_txt {
          color: #fff !important;
        }

        .light_txt {
          color: #fff;
          opacity: 0.6;
        }
      }

      .textpolish_icon {
        span {
          color: #fff !important;
        }
      }



      .split_line {
        opacity: 0.2;
      }

    }
  }

  .copyright {
    position: absolute;
    background: #fff;
    z-index: 2;
    bottom: 0;
    right: 23px;
    left: 0;
    height: 25px;
    font-size: 12px;
    color: #999;
    text-align: right;
    padding-right: 30px;
    line-height: 25px;

    img {
      width: 42px;
      margin-top: -4px;
    }
  }

  .topbox {
    position: fixed;
    right: 56px;
    bottom: 104px;
    width: 40px;
    height: 40px;
    z-index: 2;
    cursor: pointer;
  }
}