import { join } from 'path';
import { app, crashReporter } from 'electron';
import extract from 'extract-zip';
import { spawn } from 'child_process';
import fs from 'fs';
import sudo from 'sudo-prompt';
import { errorWriteLocal } from './errorWriteLocal';
import config from './config';

const APP_PATH = 'AudioUnitMac.app/Contents/MacOS/AudioUnitMac';
const DRIVER_NAME = 'yxtaudioproxy.driver';
const TARGET_PATH = '/Library/Audio/Plug-Ins/HAL';

// 获取资源路径
const getResourcePath = (): string => {
    return join(
        config.isPackaged ? process.resourcesPath : process.cwd(),
        'build/extraResources'
    );
};

/**
 * 解压 Mac 相关资源文件
 */
export async function macInstall(): Promise<void> {
    // 开发环境系统不执行
    if (process.env.NODE_ENV === 'development') {
        console.log('macInstall development')
        return;
    }

    const resourcePath = getResourcePath();
    const source = join(resourcePath, 'mac.zip');
    const targetDir = resourcePath;

    errorWriteLocal('Mac resources extraction - Source:', source);
    errorWriteLocal('Mac resources extraction - Target:', targetDir);

    try {
        await extract(source, { dir: targetDir });
        errorWriteLocal('Mac resources extraction completed');
    } catch (err: any) {
        errorWriteLocal('Mac resources extraction failed:', err?.message);
        throw new Error(`Failed to extract mac resources: ${err?.message}`);
    }
}

// 获取驱动路径
const getDriverPath = () => {
    const resourcePath = getResourcePath();
    const driverPath = join(resourcePath, DRIVER_NAME);
    const appPath = join(resourcePath, APP_PATH);
    return { driverPath, appPath };
};

// 检查是否有新版本的驱动
async function hasNewDriverVersion(): Promise<boolean> {
    try {
        const { driverPath } = getDriverPath();
        const installedPlistPath = join(TARGET_PATH, DRIVER_NAME, 'Contents/Info.plist');

        if (!fs.existsSync(installedPlistPath)) {
            return true;
        }

        const installedVersion = await getDriverVersion(installedPlistPath);
        const newVersion = await getDriverVersion(join(driverPath, 'Contents/Info.plist'));

        return installedVersion !== newVersion;
    } catch (err) {
        errorWriteLocal('hasNewDriverVersion error:', err);
        return true;
    }
}

// 获取驱动版本
async function getDriverVersion(plistPath: string): Promise<string> {
    try {
        const plistContent = fs.readFileSync(plistPath, 'utf8');
        const versionMatch = plistContent.match(/<key>CFBundleVersion<\/key>\s*<string>([^<]+)<\/string>/);
        return versionMatch ? versionMatch[1] : '';
    } catch (err) {
        errorWriteLocal('getDriverVersion error:', err);
        return '';
    }
}

// 启动音频应用（优化版本）
async function startAudioApp(): Promise<boolean> {
    errorWriteLocal('startAudioApp');
    if (global.virtualAudioProcess && !global.virtualAudioProcess.killed) {
        errorWriteLocal('startAudioApp result ready has');
        return true;
    }

    try {
        const { appPath } = getDriverPath();

        // 优化进程启动参数，减少CPU使用
        global.virtualAudioProcess = spawn(appPath, [], {
            stdio: 'ignore', // 忽略标准输入输出，减少进程开销
            detached: false, // 不分离进程，便于管理
            windowsHide: true // 隐藏窗口
        });

        // 设置进程优先级（如果支持）
        if (global.virtualAudioProcess.pid) {
            try {
                // 在Mac上设置较低的进程优先级
                process.kill(global.virtualAudioProcess.pid, 0); // 检查进程是否存活
            } catch (err) {
                errorWriteLocal('Process priority setting failed:', err);
            }
        }

        errorWriteLocal('startAudioApp result success');
        return true;
    } catch (err) {
        errorWriteLocal('Start audio app failed:', err);
        return false;
    }
}

// 安装驱动
function installDriver(): Promise<boolean> {
    return new Promise((resolve) => {
        errorWriteLocal('installDriver');
        const { driverPath } = getDriverPath();
        // @ts-ignore
        const options: sudo.Options = { name: 'NovaGuide' };

        let command = `rm -rf "${TARGET_PATH}/${DRIVER_NAME}" && cp -r "${driverPath}" "${TARGET_PATH}" && launchctl kickstart -kp system/com.apple.audio.coreaudiod`;

        if (!fs.existsSync(TARGET_PATH)) {
            command = `mkdir -p "${TARGET_PATH}" && ${command}`;
        }

        errorWriteLocal(`Installing audio driver: ${driverPath} to ${TARGET_PATH}`);

        sudo.exec(command, options, async (error: Error | null) => {
            if (error) {
                errorWriteLocal('Driver installation error:', error?.message);
                resolve(false);
                return;
            }
            resolve(true);
        });
    });
}

// 检查驱动是否已安装
async function checkDriverInstalled(): Promise<boolean> {
    try {
        const installedPlistPath = join(TARGET_PATH, DRIVER_NAME, 'Contents/Info.plist');
        return fs.existsSync(installedPlistPath);
    } catch (err) {
        errorWriteLocal('checkDriverInstalled error:', err);
        return false;
    }
}

// 安装并启动虚拟声卡（优化版本）
export async function installMacVirtualAudio(): Promise<boolean> {
    errorWriteLocal('installMacVirtualAudio')
    if (!config.isMac) {
        return true
    }

    try {
        // 检查驱动是否已安装
        const hasDriver = await checkDriverInstalled();
        const hasNewVersion = await hasNewDriverVersion();

        if (!hasDriver || hasNewVersion) {
            const installResult = await installDriver();
            if (!installResult) {
                errorWriteLocal('Driver installation failed');
                return false;
            }
        }

        // 启动音频应用
        return await startAudioApp();
    } catch (err) {
        errorWriteLocal('installMacVirtualAudio error:', err);
        return false;
    }
}

// 只检查驱动是否安装
export async function checkMacVirtualAudio(): Promise<boolean> {
    errorWriteLocal('checkMacVirtualAudio')
    try {
        const installedPlistPath = join(TARGET_PATH, DRIVER_NAME, 'Contents/Info.plist');
        const hasDriver = fs.existsSync(installedPlistPath);
        errorWriteLocal('checkMacVirtualAudio result', hasDriver)
        return hasDriver;
    } catch (err) {
        errorWriteLocal('checkMacVirtualAudio error:', err);
        return false;
    }
}

// 优化虚拟音频进程管理
export function killVirtualAudio() {
    if (config.isMac && global.virtualAudioProcess) {
        errorWriteLocal('killVirtualAudio', global.virtualAudioProcess.pid)
        try {
            // 优雅地终止进程
            global.virtualAudioProcess.kill('SIGTERM');

            // 如果进程没有及时退出，强制终止
            setTimeout(() => {
                if (global.virtualAudioProcess && !global.virtualAudioProcess.killed) {
                    global.virtualAudioProcess.kill('SIGKILL');
                }
            }, 2000);

            global.virtualAudioProcess = null;
        } catch (err) {
            errorWriteLocal('killVirtualAudio error:', err);
        }
    }
}

export function initCrashReporter() {
    crashReporter.start({
        productName: 'NovaGuide',
        companyName: '江苏云学堂网络科技有限公司',
        submitURL: '', // 本地存储不需要提交URL
        uploadToServer: false
    })
}