/*
 * @Author: zqx <EMAIL>
 * @Date: 2025-07-16 10:45:16
 * @LastEditors: zqx <EMAIL>
 * @LastEditTime: 2025-07-30 11:05:54
 * @FilePath: /XMate-h5/src/directives/aiTip.js
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
export default {
    mounted(el, binding) {
        const tip = document.createElement('div');

        // 支持 binding.value 为字符串或对象
        let type = '';
        let textContent = '';
        if (typeof binding.value === 'string') {
            type = binding.value;
            if (type == 'bottom-right') {
                textContent = 'AI生成';
            } else {
                textContent = '以上内容由AI生成，仅供参考';
            }
        } else if (typeof binding.value === 'object' && binding.value !== null) {
            type = binding.value.type || 'center';
            if (binding.value.text) {
                textContent = binding.value.text;
            } else {
                textContent = '以上内容由AI生成，仅供参考';
            }
        }

        tip.className = 'ai-generated-tip';

        if (type === 'center') {
            tip.style.textAlign = 'center';
            tip.style.color = '#8C8C8C';
            tip.style.fontSize = '12px';
            tip.style.marginTop = '16px';
            tip.style.paddingBottom = '6px';
            tip.style.width = '100%';
            tip.innerText = textContent;
        } else if (type === 'bottom-left') {
            tip.style.textAlign = 'left';
            tip.style.color = '#8C8C8C';
            tip.style.fontSize = '12px';
            tip.style.marginTop = '16px';
            tip.style.paddingBottom = '6px';
            tip.style.width = '100%';
            tip.innerText = textContent;
        } else if (type === 'bottom-right') {
            tip.style.position = 'absolute';
            tip.style.right = '0px';
            tip.style.bottom = '0px';
            tip.style.borderBottomRightRadius = '8px';
            tip.style.background = 'linear-gradient(270deg, rgba(239, 242, 250, 1) 0%, rgba(239, 242, 250, 0) 100%)';
            const text = document.createElement('div');
            text.innerText = textContent;
            text.style.marginLeft = '8px';
            text.style.color = '#8C8C8C';
            text.style.fontSize = '12px';
            text.style.whiteSpace = 'nowrap';
            text.style.lineHeight = '20px';
            text.style.padding = '0 12px';

            // 保证父元素是相对定位
            if (getComputedStyle(el).position === 'static') {
                el.style.position = 'relative';
            }
            // 在原有 padding-bottom 基础上增加20px
            const originPadding = window.getComputedStyle(el).paddingBottom;
            const originValue = parseFloat(originPadding) || 0;
            el.style.paddingBottom = (originValue + 20) + 'px';

            tip.appendChild(text);
        }

        el.appendChild(tip);
    }
}; 