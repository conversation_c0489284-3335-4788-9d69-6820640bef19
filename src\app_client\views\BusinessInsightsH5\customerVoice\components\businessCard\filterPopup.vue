<template>
    <van-action-sheet v-model:show="actionSheetShow" title="筛选" class="action-sheet" @close="onClose">
        <div class="div1">
            <span class="p1">客户</span>
            <span class="arrow-icon" @click="onOpenSearch">
                <span class="single-line-ellipsis span1"> {{ customerLabel ||
                    '请选择' }} </span>
                <van-icon name="arrow" />
            </span>
        </div>
        <div class="div2">
            <p class="p1">情感</p>
            <div class="option-list">
                <div v-for="(item) in emotionList" :key="item.value"
                    :class="['option-btn', { active: emotion === item.value }]" @click="handleSelectEmotion(item)">
                    {{ item.label }}
                </div>
            </div>
        </div>

        <div class="action-sheet-footer">
            <van-button @click="onReset">
                重置
            </van-button>
            <van-button type="primary" block @click="onConfirm">
                确认
            </van-button>
        </div>
    </van-action-sheet>
    <search ref="searchRef" v-model="customer" :data="customerList"></search>

</template>
<script setup>
import search from './search.vue';
const emit = defineEmits(['close'])
const emotionList = [
    {
        value: '1',
        label: '积极态度'
    }, {
        value: '2',
        label: '消极态度'
    }
]
const emotion = ref('')
const customer = ref([])
const searchRef = ref(null)
const actionSheetShow = ref(false);
const customerList = computed(() => g.clientBiStore.cvCustomerList.map(x => {
    return {
        customerId: x.id,
        customerName: x.name,
    }
}))
const customerLabel = computed(() => [...customerList.value].filter(i => customer.value.includes(i.customerId)).map(i => i.customerName).join(','))

const init = () => {
    actionSheetShow.value = true
}

const onOpenSearch = () => {
    nextTick(() => {
        searchRef.value.init()
    })
}
const handleSelectEmotion = (item) => {
    emotion.value = item.value
}
const onClose = () => {
    emit('close',
        emotion.value || (customer.value && customer.value.length)
    )
}


const onReset = () => {
    customerLabel.value = ''
    emotion.value = ''
    customer.value = []
}
const onConfirm = () => {
    const tab = g.clientBiStore.selectTabType
    if (tab === 'CUSTOMER_VOICE') {
        g.clientBiStore.setFilterCondition({
            emotion: emotion.value,
            customer: customer.value,
        })
        g.clientBiStore.updateCvFbVersion()
    } else if (tab === 'COMPETITOR_RADAR') {
        g.clientBiStore.setCrFilterCondition({
            emotion: emotion.value,
            customer: customer.value,
        })
        g.clientBiStore.updateCrFbVersion()
    }
    actionSheetShow.value = false;
}

onMounted(async () => {
    await g.clientBiStore.getCvCustomerList()
})

watch(() => g.clientBiStore.periodType, () => {
    customerLabel.value = ''
    emotion.value = ''
    customer.value = []
}, { immediate: true })


defineExpose({
    init
})

</script>
<style lang="scss" scoped>
.action-sheet {
    p {
        margin: 0;
    }

    .div1 {
        padding: 20px;
        font-size: 12px;
        border-bottom: 1px solid #E9E9E9;
        display: flex;
        justify-content: space-between;
        align-items: center;



        .arrow-icon {
            color: #8C8C8C;
            display: flex;

            align-items: center;
        }

        .span1 {
            display: inline-block;
            width: 150px;
            text-align: right;
        }

    }

    .div2 {
        padding: 20px;

        .option-list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 8px;
            margin: 8px 0;

            .option-btn {
                padding: 5px 12px;
                background: #F4F5F5;
                border-radius: 4px;

                font-size: 12px;
                color: #595959;
                cursor: pointer;
                transition: all 0.2s;
                word-break: keep-all;


                &.active {
                    background: #F0F3FF;
                    color: #436BFF;

                }
            }
        }




    }

    .p1 {
        font-weight: 700;
        font-size: 12px;
        color: #262626;
        line-height: 18px;
    }

    .action-sheet-footer {
        padding: 20px 15px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-top: 1px solid #E9E9E9;

        button {
            width: calc(50% - 12px);
        }
    }
}
</style>