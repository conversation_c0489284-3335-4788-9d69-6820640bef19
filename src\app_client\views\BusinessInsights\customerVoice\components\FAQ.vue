<template>
    <div class="faq-wrapper">
        <div class="faq-container">
            <!-- 标题和统计信息区域 -->
            <div class="faq-header-box">
                <h2 class="cvh_title">用户常见问题（FAQ） <el-tooltip content="展示用户反馈中最常出现的问题及其所属维度" placement="top"
                        popper-class="custom-tooltip">
                        <img :src="getOssUrl('question.png', 3)" alt="faq" class="faq_icon" />
                    </el-tooltip>
                </h2>
                <el-button type="default" @click="exportFaq" :loading="loadingExport"
                    class="faq-export-btn">导出Excel</el-button>
            </div>

            <!-- 内容区域 -->
            <div class="faq-content-box">
                <div class="faq-header" v-loading="loadingStats">
                    <div class="faq-stats">
                        <div class="stat-item" :class="{ 'active': selectedDimension === '' }"
                            @click="toggleDimension('')">
                            <div class="stat-value">100%</div>
                            <div class="stat-label">全部</div>
                        </div>

                        <div class="stat-item" v-for="(value, key) in stats" :key="key"
                            :class="{ 'active': selectedDimension === value.dimensionId }"
                            @click="toggleDimension(value.dimensionId)">
                            <div class="stat-value">{{ value.qaRatio }}% <el-tooltip raw-content v-if="key === 0"
                                    content="分子：在该维度下，分母中对应的问答回顾数。<br/>分母：在选定的时间范围内，常见问题对应的问答回顾总数。" placement="top"
                                    popper-class="custom-tooltip">
                                    <img :src="getOssUrl('question.png', 3)" alt="faq" class="faq_icon" />
                                </el-tooltip></div>
                            <div class="stat-label">{{ value.dimensionName }}</div>
                        </div>
                    </div>
                </div>
                <div class="faq-content">
                    <!-- 左侧TOP10问题列表 -->
                    <div class="faq-list" v-loading="loadingLeft">
                        <div class="list-items">
                            <div v-for="(item, index) in faqList" :key="item.dimensionId" class="list-item"
                                :class="{ active: selectedFaq === item.dimensionId }" @click="onClickLeft(item)">
                                <div class="item-index">{{ index + 1 }}</div>
                                <div class="item-content">
                                    <el-tooltip :content="item.question" :disabled="isHide" :show-after="500">
                                        <div class="item-title single-line-ellipsis"
                                            @mouseover="isShowTooltip(item.question, $event)">
                                            {{ item.question }}
                                        </div>
                                    </el-tooltip>
                                    <div :class="['item-tag', `tag_${getTagClass(item.dimensionName)}`]">{{
                                        item.dimensionName }}</div>
                                </div>
                                <div class="item-count">{{ item.qaCount }}条</div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧详情展示 -->
                    <div class="faq-detail">
                        <div class="detail-content">
                            <div class="detail-header"> 问答回顾 </div>
                            <!-- <aiAnalysis :data="aiAnswer" style="margin-bottom: 12px;" :loading="false" title="AI 推荐回复"
                                :defaultExpanded="false">
                                <template #button>
                                <div class="faq-analysis-button pdf_change_style " @click.stop="onAddLibrary"><span
                                        class="pdf_change_style">加入案例库</span></div>
                            </template>
</aiAnalysis> -->
                            <QAList :qaList="qaList" :loading="loading" />
                        </div>
                        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" background
                            layout="total,  prev, pager, next" :total="parseInt(totalQANum)"
                            @current-change="handleCurrentChange" v-show="totalQANum > 0"
                            class="checkSource_wrap_pagination" />
                    </div>
                </div>
            </div>
        </div>
        <ModalAddLibrary ref="modalAddLibraryRef" />
    </div>
</template>

<script setup>
import aiAnalysis from "../../components/aiAnalysis.vue"
import QAList from "@/components/QAList.vue"
import { ElTooltip } from 'element-plus'
import ModalAddLibrary from "./ModalAddLibrary.vue";
import { getOssUrl } from "@/js/utils.js";
import { exportUserFaq } from "@/app_client/tools/api.js";

const loadingAiAnswer = ref(false)
const aiAnswer = ref('')
const store = g.clientBiStore
const modalAddLibraryRef = ref(null)
const loading = ref(false)
const loadingStats = ref(false)
const loadingLeft = ref(false)
const totalNum = ref(0)
// 选中的FAQ id
const selectedFaq = ref(0)
const loadingExport = ref(false)

// 选中的维度ID
const selectedDimension = ref('')
// 统计数据
const stats = computed(() => g.clientBiStore.cvFaqDistList)

const onClickLeft = (item) => {
    selectedFaq.value = item.dimensionId
    currentPage.value = 1
    queryMainData()
    // queryAiAnswer()
}

// 分页相关变量
const currentPage = ref(1)
const pageSize = ref(4)
const totalQANum = ref(0)
const isHide = ref(true)
const isShowTooltip = (val, e) => {
    isHide.value = g.appStore.isShowTooltip(val, e, 14, 1);
}

// const queryAiAnswer = async () => {
//     loadingAiAnswer.value = true
//     aiAnswer.value = await g.clientBiStore.getFaqRecomendAnswer(selectedFaq.value)
//     loadingAiAnswer.value = false
// }
// 导出用户问题
const exportFaq = async () => {
    const param = {
        days: g.clientBiStore.gFilterDays,
        filename: "用户常见问题（FAQ）"
    }
    loadingExport.value = true
    await exportUserFaq(param)
    loadingExport.value = false
}


// FAQ列表数据
const faqList = computed(() => {
    let list = g.clientBiStore.cvFaqQuestionsCount
    if (selectedDimension.value) {
        list = list.filter(item => item.parentDimensionId === selectedDimension.value)
    }
    return list
})

// 问答列表数据
const qaList = computed(() => {
    const listP = g.clientBiStore.cvFaqQAListP;
    totalQANum.value = listP.totalNum;
    return listP.datas;
})

// 获取元素引用

const queryMainData = async () => {
    // main data
    const param = {
        "pageSize": pageSize.value,
        "pageNumber": currentPage.value,
    }

    if (selectedFaq.value) {
        param.dimensionIds = [selectedFaq.value]
    }
    if (!param.dimensionIds) return
    console.log(selectedFaq.value)
    loading.value = true
    await store.getCvFaqQAList(param)
    loading.value = false
}

const queryData = async () => {
    //stats
    const f1 = async () => {
        loadingStats.value = true;
        await store.getCvFaqDistList()
        loadingStats.value = false;
    }
    //left qa count
    const f2 = async () => {
        loadingLeft.value = true;
        await store.getCvFaqQuestionsCount()
        loadingLeft.value = false;
    }
    f1()
    f2()
    // currentPage.value = 1

    // queryMainData()
}

const handleCurrentChange = (val) => {
    currentPage.value = val
    queryMainData()
}

// 切换维度选中状态
const toggleDimension = (dimensionId) => {
    if (selectedDimension.value === dimensionId) {
        return
    } else {
        selectedDimension.value = dimensionId
    }

    // 重置选中的FAQ
    selectedFaq.value = 0
    aiAnswer.value = ''
    // 重置分页
    currentPage.value = 1

    // 等待下一个tick，确保faqList已经更新，然后选中第一个FAQ
    // nextTick(() => {
    //     if (faqList.value.length > 0) {
    //         console.log(faqList.value, 33)
    //         queryMainData()
    //         // queryAiAnswer()
    //     }
    // })
}

const onAddLibrary = () => {
    modalAddLibraryRef.value.open()
}

// 监听选中的FAQ索引变化
watch(() => [g.clientBiStore.periodType], async (newVal, oldVal) => {
    if (newVal !== oldVal) {
        queryData()
    }
}, { immediate: true })

watch(() => [faqList.value], async (newVal, oldVal) => {
    if (faqList.value.length > 0) {
        selectedFaq.value = faqList.value[0].dimensionId
        queryMainData()
    }
}, { immediate: true, deep: true })

onMounted(async () => {
    queryData()
})


const getTagClass = (name) => {
    const index = stats.value.findIndex(i => i.dimensionName == name)
    if (index > -1) return index + 1
    return 1
}
</script>

<style lang="scss" scoped>
.faq-wrapper {
    .faq-container {
        margin-top: 28px;

        .faq_icon {
            width: 16px;
            height: 16px;
            margin-left: 4px;
        }

        .faq-header-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            padding-right: 15px;

            .faq-export-btn {
                background: rgba(255, 255, 255, 0.01);
                border-radius: 4px;
                border: 1px solid #436BFF;
                color: #436BFF;
                line-height: 22px;
            }
        }

        .faq-content-box {
            background: #F9FAFC;
            border-radius: 8px;
            padding: 24px;
            margin: 24px 15px 0 15px;
            box-sizing: border-box;

            .faq-header {
                .faq-stats {
                    display: flex;
                    margin-bottom: 24px;
                    gap: 12px;

                    .stat-item {
                        background: #fff;
                        border-radius: 8px;
                        padding: 12px 24px;
                        width: 120px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        border-radius: 8px;
                        border: 1px solid #fff;

                        &.active {
                            border: 1px solid #436BFF;

                            div {
                                color: #436BFF;
                            }
                        }

                        .stat-value {
                            font-size: 24px;
                            font-weight: 600;
                            color: #262626;
                            display: flex;
                            align-items: center;
                        }

                        .stat-label {
                            font-size: 14px;
                            color: #8C8C8C;
                            margin-top: 4px;
                        }
                    }
                }
            }
        }

        .faq-content {
            display: flex;
            justify-content: space-between;

            .faq-list {
                width: 30%;
                border-radius: 8px;
                background: #fff;
                box-sizing: border-box;

                .list-items {
                    overflow-y: scroll;
                    border-radius: 4px;
                    height: 650px;
                    padding: 12px 0;
                    box-sizing: border-box;
                    width: 100%;

                    .single-line-ellipsis {

                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .list-item {
                        box-sizing: border-box;
                        padding: 16px 24px;
                        display: flex;
                        align-items: center;
                        // justify-content: space-between;
                        cursor: pointer;
                        height: 50px;

                        .item-content {
                            // flex: 1;
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-start;
                            align-items: center;
                            width: calc(100% - 78px);

                            .item-title {
                                // width: calc(100% - 84px);
                            }

                            .item-tag {
                                padding: 2px 8px;
                                border-radius: 4px;
                                font-size: 12px;
                                margin-left: 12px;
                                box-sizing: border-box;


                                &.tag_1 {
                                    background: rgba(51, 196, 255, 0.10);
                                    color: #33C4FF;
                                }

                                &.tag_2 {
                                    background: rgba(4, 204, 164, 0.10);
                                    color: #04CCA4;
                                }

                                &.tag_3 {
                                    background: rgba(246, 189, 22, 0.10);
                                    color: #F6BD16;
                                }

                                &.tag_4 {
                                    background: rgba(255, 107, 59, 0.10);
                                    color: #FF6B3B;
                                }

                                &.tag_5 {
                                    background: rgba(91, 143, 249, 0.10);
                                    color: #5B8FF9;
                                }

                                &.tag_6 {
                                    background: rgba(255, 117, 173, 0.10);
                                    color: #FF75AD;
                                }

                                &.tag_7 {
                                    background: rgba(141, 122, 242, 0.10);
                                    color: #8D7AF2;
                                }

                                &.tag_8 {
                                    background: rgba(40, 156, 203, 0.10);
                                    color: #289CCB;
                                }
                            }

                            .item-title {
                                font-size: 14px;
                                color: #262626;
                            }
                        }

                        &:hover {
                            background: #F5F5F5;
                        }

                        &.active {
                            background: #F0F6FF;

                            .item-title {

                                // flex: 1;
                                color: #436BFF;
                            }
                        }

                        .item-index {
                            width: 24px;
                            color: #436BFF;
                            font-size: 14px;
                            font-weight: 600;
                        }


                        .item-count {
                            color: #8C8C8C;
                            font-size: 14px;
                            min-width: 50px;
                            text-align: right;
                        }
                    }
                }
            }

            .faq-detail {
                border-radius: 4px;
                padding-left: 12px;
                width: 70%;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                height: 680px;

                .detail-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                }

                .checkSource_wrap_pagination {
                    height: 36px;
                    display: flex;
                    justify-content: flex-end;
                    // margin: 8px 0 0 8px !important;
                    // margin: 12px 24px;
                }

                .detail-header {
                    padding: 0 8px 12px;
                    font-size: 14px;
                    font-weight: 700;
                    color: #262626;
                }

            }
        }
    }
}

.faq-analysis-button {
    font-size: 14px;
    font-weight: bold;
    position: relative;
    padding: 5px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 12px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 4px;
        padding: 1px;
        background: linear-gradient(45deg, #6D1BFF 0%, #1D65FF 50%, #00BCE6 100%);
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
    }

    span {
        background: linear-gradient(45deg, #6D1BFF 0%, #1D65FF 50%, #00BCE6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
}
</style>

<style lang="scss" scoped>
.list-item:hover .item-tag,
.list-item.active .item-tag {
    display: inline-block !important;
}
</style>